# Notes/Discussions

## 1. Dataset Authoring Scenarios

**Technical Dataset**

- Scan
- Import using Public API
- Datastore UI
  - Author Datasets of type - Must be configurable
  - Author Columns
  - Author Constraints
  - Fetch
    - context_id, parent_id, type, name, description
    - automatically returns all relations for the results
  - No support as yet for
    - POST, PUT, DELETE /entities
    - POST, PUT, DELETE /elements
    - POST, PUT, DELETE /relationships
- Content import wants to define Dataset and Fields
- Profile Tables and columns

**Business Dataset**

- UI
  - Is there a single UI to define these Datasets?
    - Are they exposed only within a Subject Area?
  - Define a `UI` type of Dataset
  - Define a `Query` type of Dataset that can
    - Map business Fields to technical Columns
    - Map the business dataset to a technical Table or View
    - Map to a SQL query definition (SQL query builder UI)
    - Map to another business dataset
    - Map to another business dataset with incremental query filters
  - Current implementation: Business Dataset is restricted to work with a single Datastore
    - Should this be carried forward to the new Business Dataset definition as well?
- Public API
- Construct APP
  - Are these Construct Dataset defined within a Subject Area?
- Migration of Business Dataset to new model
  - Maybe as QUERY

## 2. Datastore API

- `/datastores`
  - post
    put
    get
    delete

## 3. DataModel API (always created within a context)

| Action             | HTTP Verb  | URI                                                          | Notes                                            |
| ------------------ | ---------- | ------------------------------------------------------------ | ------------------------------------------------ |
| Create full model  | **POST**   | `/contexts/{contextId}/datamodel`                            | POST → create new model; response `201 Created`. |
| Read full model    | **GET**    | `/contexts/{contextId}/datamodel`                            |                                                  |
| Replace full model | **PUT**    | `/contexts/{contextId}/datamodel`                            | Idempotent full-model replace.                   |
| Partially update   | **PATCH**  | `/contexts/{contextId}/datamodel`                            | Idempotent partial update.                       |
| Delete model       | **DELETE** | `/contexts/{contextId}/datamodel`                            |                                                  |
|                    |            |                                                              |                                                  |
| Model navigation   | **GET**    | `/contexts/{contextId}/datamodel/navigation`                 |                                                  |
|                    |            |                                                              |                                                  |
| Read entities      | **GET**    | `/contexts/{contextId}/datamodel/entities`                   |                                                  |
| Read single        | **GET**    | `/contexts/{contextId}/datamodel/entities/{entityId}`        |                                                  |
|                    |            |                                                              |                                                  |
| Read elements      | **GET**    | `/contexts/{contextId}/datamodel/elements`                   |                                                  |
| Read single        | **GET**    | `/contexts/{contextId}/datamodel/elements/{elementId}`       |                                                  |
|                    |            |                                                              |                                                  |
| Read relationships | **GET**    | `/contexts/{contextId}/datamodel/relationships`              |                                                  |
| Read single        | **GET**    | `/contexts/{contextId}/datamodel/relationships/{relationId}` |                                                  |
|                    |            |                                                              |                                                  |
| Import start       | **POST**   | `/datamodel-import/start`                                    | Get import transaction ID for give context_id    |
| Import batches     | **POST**   | `/datamodel-import/{importId}/batch`                         | API for batch import using single transaction ID |
| Import commit      | **POST**   | `/datamodel-import/{importId}/commit`                        | Commit all batches asynchronously                |
| Import status      | **GET**    | `/datamodel-import/{importId}/status`                        | Get import status for transaction ID             |

## 4. Scan and Import

- Scan
  - scan results: S3
  - full new set of data model for database tabe, view
    - mark deleted tables
- ZINC
  - KG: Update the SystemScan object status -> Injesting
  - VALIDATE
    - Read the scan file, validate i
  - INGEST
    - Build the relevant entity-element-relationship
    - import_id = call /import-start
    - Send in batches /import
      - Send the data model with correct operations
      - Send the scan_id as activity_property
    - Commit /import-commit
    - Call /import-status
  - KG: Update the SystemScan object status
    - Success: -> Complete
    - Fail: Put back the msg to the queue
- Catalog
  - Based on the operation type, update the data model

### 4.1 Import Data Model Request

# Option 1

```json
{
  "entities": [
    {
      "model_name": "Product",
      "operation": "replace", //add, delete, replace (full rewrite, idempotent), update_properties
      "name": "Product",
      "type": "TABLE",
      "consumption_type": "dataset",
      "elements": [
        {
          "model_name": "Product.ProductID",
          "name": "ProductID",
          "type": "COLUMN",
          "consumption_type": "field",
          "scan_properties": {"ssc_id"}
        }
      ]
    },
    {
      "name": "ProductCategory",
      "operation": "delete"
    }
  ],
  "relationships": [
    {
      "name": "PK_Production_Product_ProductID",
      "operation": "replace",
      "type": "PK_REF",
      "description": "",
      "source": "#element/Product.PK_ProductID",
      "target": "#element/Product.ProductID"
    }
  ]
}
```

## Option 2

```json
{
  "meta_schema_version": "relational-20250201",
  "entities": [
    {
      "operation": "replace", //add, delete, replace (full rewrite, idempotent), update_properties
      "name": "Product", //"product"
      "type": "TABLE",
      "consumption_type": "dataset",
      "elements": [
        {
          "name": "ProductID",
          "type": "COLUMN",
          "consumption_type": "field"
        }
      ]
    },
    {
      "entity_name": "Product",
      "operation": "update",
      "description": "This an updated description",
      "properties": {
        "profiling": {}
      }
    },
    {
      "entity_name": "SalesHistory",
      "operation": "delete"
    }
  ],
  "elements": [
    {
      "entity_name": "Employee",
      "operation": "replace", //add, delete, replace (full rewrite, idempotent), update_properties
      "name": "FirstName",
      "type": "COLUMN",
      "consumption_type": "field"
    },
    {
      "entity_name": "Employee",
      "name": "LastName",
      "operation": "update",
      "description": "This an updated description"
    },
    {
      "entity_name": "Employee",
      "name": "PayRate",
      "operation": "delete"
    }
  ],
  "relationships": [
    {
      "name": "PK_Production_Product_ProductID",
      "operation": "replace",
      "type": "PK_REF",
      "description": "",
      "source": "#element/Product.PK_ProductID",
      "target": "#element/Product.ProductID"
    }
  ]
}
```

### 4.2 Update Data Model Request

```json
{
  "meta_schema_version": "relational-20250201",
  "current_version": "20250201-001",

  "entities": [
    {
      "operation": "replace", //add, delete, replace (full rewrite, idempotent), update_properties
      "name": "Product",
      "type": "TABLE",
      "consumption_type": "dataset",
      "elements": [
        {
          "name": "ProductID",
          "type": "COLUMN",
          "consumption_type": "field"
        }
      ]
    },
    {
      "id": "ent_5678",
      "operation": "update",
      "description": "This an updated description",
      "properties": {
        "profiling": {}
      }
    },
    {
      "id": "ent_2345",
      "operation": "delete"
    }
  ],
  "elements": [
    {
      "entity_id": "ent_1234",
      "operation": "replace", //add, delete, replace (full rewrite, idempotent), update_properties
      "name": "OrderName",
      "type": "COLUMN",
      "consumption_type": "field"
    },
    {
      "id": "ele_5678",
      "operation": "update",
      "description": "This an updated description"
    },
    {
      "id": "ele_2345",
      "operation": "delete"
    }
  ],
  "relationships": [
    {
      "name": "PK_Production_Product_ProductID",
      "operation": "replace",
      "type": "PK_REF",
      "description": "",
      "source": "#element/Product.PK_ProductID",
      "target": "#element/Product.ProductID"
    },
    {
      "id": "rel_5678",
      "operation": "update",
      "description": "This an updated description"
    },
    {
      "id": "rel_2345",
      "operation": "delete"
    }
  ]
}
```

## 5. Activity specific set of properties

1. Register valid properties applicable to an "activity" in the meta-meta definition
   - activity_name
   - entity_types
   - activity_properties
   - service (migrate)
   - Example:
     - name: profiling-results
     - entity_types: TABLE
       - properties
         - record_count
         - null_count
     - name: profiling_results
     - entity_types: COLUMN
       - properties
         - null_count
2. When applying an activity specific properties

```json
{
  "entities": [
    {
      "model_name": "product",
      "name": "Product",
      "type": "TABLE",
      "description": "Product table",
      "active": true,
      "tags": [],
      "properties": {
        "schema": "Production"
      },
      "profiling_results": {
        "record_count": 10
      },
      "entity_elements": {
        "name": "Name",
        "profiling_results": {
          "nulls_count": 10
        }
      }
    }
  ]
}
```

3. Database Storage

**Option 1**

```sql
CREATE TABLE entities (
    id TEXT NOT NULL,
    tenant_id TEXT NOT NULL,
    UNIQUE (tenant_id, id),
    meta_version TEXT NOT NULL,
    model_id TEXT,
    context_id TEXT,

    -- standard properties
    name TEXT,
    entity_type TEXT,
    description TEXT,
    active BOOLEAN,
    index INT,
    tags TEXT[],

    -- type specific properties
    properties JSONB,

    -- custom properties
    custom_properties JSONB,

    -- activity properties // CONCERN: SIZE OF EACH ROW + ANY UPDATE WILL REPLICATE ENTIRE CONTENT TO HISTORY
    activity_properties JSONB,

    -- versiong and branching information
    version_id TEXT,
    branch_id TEXT,

    -- validity range for temporal table
    sys_period tstzrange NOT NULL DEFAULT tstzrange(current_timestamp, null)
) PARTITION BY LIST(tenant_id);

```

**Example entry in DB column: activity_properties**

```json
{
  "name": "Employee",
  "type": "TABLE",
  "properties": { "schema": "dbo" },
  // ...
  "activity_properties": {
    "profiling-results": { "record_count": 10, "profiling_time": "" },
    "migrate-snapshot": { "zdeploy": "qa" },
    "replicate-target": { "prefix": "donna" }
  },
  // ...
  "version_id": "ver_12345",
  "branch_id": "brh_12345",
  "sys_period": ["2025-05-21", ""]
}
```

**Option 2**

- activity_properties
- activity_properties_history

```sql
CREATE TABLE activity_properties (
    -- can be an entity or element ID
    model_element_id TEXT NOT NULL,

    activity_id TEXT NOT NULL

    -- activity properties
    activity_properties JSONB,

    -- versiong and branching information
    version_id TEXT,
    branch_id TEXT,

    -- validity range for temporal table
    sys_period tstzrange NOT NULL DEFAULT tstzrange(current_timestamp, null)
) PARTITION BY LIST(tenant_id);

```

| model_element_id | activity_id | activity_properties| version_id | branch_id|
| ent_001 | ..|

**Example entry in DB TABLE: activity_properties**

```json
{
  "model_element_id": "ent_001",
  "activity_id": "act_0001-scan",
  "activity_properties": { "scan_id": "ssc_1101" },
  "version_id": "ver_12345",
  "branch_id": "brh_12345",
  "sys_period": ["2025-05-21", ""]
}

{
  "model_element_id": "ent_001",
  "activity_id": "act_0002-profiling",
  "activity_properties": { "record_count": 10, "profiling_time": "" },
  "version_id": "ver_12345",
  "branch_id": "brh_12345",
  "sys_period": ["2025-05-21", ""]
}

{
  "model_element_id": "ent_001",
  "activity_id": "act_0003-migrate",
  "activity_properties": { "zdeploy": "qa" },
  "version_id": "ver_12345",
  "branch_id": "brh_12345",
  "sys_period": ["2025-05-21", ""]
}
```

## 6. SQL Query Storage

### Example 1

_SQL Sub Query_

```
SELECT CustomerID, SUM(Amount) AS TotalSalesVolume
  FROM [SalesOrders]
 WHERE Status = 'OPEN'
 GROUP BY CustomerID
 ORDER BY CustomerID
```

_View using sub query_

```
SELECT ac.CustomerID,
       ac.Name,
       ac.Country,
       ac.PaymentTerm,
       csv.TotalSalesVolume
  FROM [ActiveCustomers] ac LEFT JOIN
       [CustomerSalesVolume] csv ON ac.CustomerID = csv.CustomerID
 ORDER BY ac.CustomerID
```

## What needs to be captured?

- Define Dataset as "Virtual View" type
- Capture columns that make up this view
  - Capture the source columns to which this maps
- Capture the query definition
- Capture Affected_Table
- Capture Affected_Columns
- Ensure we can show lineage in future
- Ensure we can check if there are any referenced elements/entity when something is being deleted

## Storage: Option 1

- Entity (Dataset)
  - Element (columns that make up this virtual view, alias names)
  - Element (query_definition)
    - properties: {"query"}
- Relationships
  - type=mapped_element
    - source = view_dataset_element
    - target = table_dataset_element
  - type=affected_table
    - source = view_dataset_entity
    - target = table_dataset_entity
  - type=affected_columns
    - source = view_dataset_element
    - target = table_dataset_element
  - type=referenced_columns
    - source = view_dataset_entity
    - target = table_dataset_element

## Storage: Option 2

- Manage these in `mappings`

  - source_id
    - text (View Dataset)
  - query_definition
    - JSONB
  - affected_tables
    - array
  - affected_columns
    - map (source_column, target_column)

- Entity (Dataset)
  - Element (columns that make up this virtual view)

## AST

```json
{
  "select": [ "<column>", "<expr1>", "<expr2>…​" ],

  "from": {
    "kind": "table" | "dataset",
    "name": "<TableName>",
    "alias": "<alias>"
  },

  // optional
  "joins": [
    {
      "type": "inner"|"left"|"right"|"full",
      "source": {
        "kind": "table" | "dataset",
        "name": "<TableName>",
        "alias": "<alias>"
      },
      "on": "<boolean_expr>"
    }
  ],

  // optional
  "where":     "<boolean_expr>",
  "groupBy":   [ {"table": "", "column": ""}],
  "having":    "<boolean_expr>",
  "orderBy":   [ {"table": "", "column": "", "direction": "ASC"|"DESC" }]
}
```

---

# Cross Team Use Cases

## Construct

### Meeting with Matt

```
dataset
	fields -> cpt_column1
```

1. How does Construct use KG Dataset?
   - Display UI page
   - Execute queries to populate the UI content
   - Add, Update, Delete operations build the necessary SQL statements
2. What queries are built using the Dataset?
   - Fetch a select list of fields
   - Derived fields are supported by actually defining a VIEW in the backend DB
   - Applying where clauses
   - Some fields may stores IDs, so to display the actual value JOIN with the foreign table
3. How are these queries built?
   - Fetch the underlying Table and Column Names
   - Build the SQL in construct code
4. Which data-catalog API is used in Construct?
   - /components?id=[cpt_123]
5. Are there any query of component ID by scan_id?
   - No
