package repo

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	dto "github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/pkg/crunch"
	"github.com/BackOfficeAssoc/pkg/probe"
)

const (
	sqlFindEntities = `
	SELECT tenant_id, id, meta_version, context_id, model_name, name, entity_type, consumption_type, description, active, ordinal_position,
	 base_type, ref, created_at, created_by, version_id, branch_id
	 FROM %[1]s
		%[2]s --where
		%[3]s --order by
		%[4]s --limit
	`
	sqlInsertEntities = `
	   INSERT INTO %[1]s (tenant_id, id, meta_version, context_id, model_name, name, entity_type, consumption_type, description, active, ordinal_position,
	 base_type, ref, created_at, created_by, version_id, branch_id)
	VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, DEFAULT, $14, $15, $16)
	    RETURNING id, created_at
	 	`
	sqlDeleteEntities = `DELETE FROM %[1]s WHERE tenant_id = $1 AND context_id = $2`
)

func getEntityTableName(tenantID string) string {
	return fmt.Sprintf("%s.entities_%s", Schema, tenantID)
}

// FindEntities retrieves entities from the database based on the provided query.
func (r *Repository) FindEntities(ctx context.Context, query dto.FindEntitiesQuery) (dto.Entities, error) {
	var entities []dto.Entity

	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("query validation failed: %w", err)
	}
	sql := fmt.Sprintf(sqlFindEntities,
		getEntityTableName(query.TenantID),
		query.Where(),
		query.OrderBy(),
		query.Limit(),
	)

	if err := r.NamedInSelect(ctx, &entities, sql, query, nil); err != nil {
		return nil, fmt.Errorf("find Entities statement failed: %w", err)
	}
	return entities, nil
}

// FindEntitiesByTypes is now part of into FindEntities

func (r *Repository) CreateEntities(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
	var err error

	// Handle empty entities array
	if len(entities) == 0 {
		// Nothing to create, return success
		return nil
	}

	tenantID := entities[0].TenantID
	if tenantID == "" {
		return fmt.Errorf("tenant ID is required")
	}

	err = createPartitions(ctx, r.DB, tenantID)
	if err != nil {
		return fmt.Errorf("failed to create partitions: %w", err)
	}

	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return fmt.Errorf("failed to begin transaction: %w", err)
		}
	}

	defer tx.Rollback()

	// Prepare batch insert
	sqlInsertStmt := fmt.Sprintf(sqlInsertEntities, getEntityTableName(tenantID))
	stmt, err := tx.PrepareContext(ctx, sqlInsertStmt)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	for i := range entities {
		id, err := crunch.Generate("ent")
		if err != nil {
			return fmt.Errorf("failed to generate ID: %w", err)
		}
		var CreatedAt string
		// Trim any whitespace from tenant ID
		tenantID := strings.TrimSpace(entities[i].TenantID)

		err = stmt.QueryRowContext(ctx,
			tenantID,
			id, // Use generated ID instead of entities[i].ID
			entities[i].MetaVersion,
			entities[i].ContextID,
			entities[i].ModelName,
			entities[i].Name,
			entities[i].Type, // Use Type instead of EntityType
			entities[i].ConsumptionType,
			entities[i].Description,
			entities[i].Active,
			entities[i].OrdinalPosition,
			entities[i].BaseType,
			entities[i].Ref,
			// Let the database handle the timestamp with DEFAULT NOW()
			entities[i].CreatedBy,
			entities[i].VersionID,
			entities[i].BranchID,
		).Scan(&id, &CreatedAt)
		if err != nil {
			return fmt.Errorf("failed to insert event: %w", err)
		}
		entities[i].ID = id
		entities[i].CreatedAt = CreatedAt
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func (r *Repository) DeleteEntities(ctx context.Context, tx *sql.Tx, params dto.DeleteEntityParams) (*dto.ExecutionEntityDeleteResult, error) {
	var err error
	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to begin transaction: %w", err)
		}
	}
	defer tx.Rollback()

	sqlDeleteStmt := fmt.Sprintf(sqlDeleteEntities, getEntityTableName(params.TenantID))
	entitiesRes, err := tx.ExecContext(ctx, sqlDeleteStmt, params.TenantID, params.ContextID)
	if err != nil {
		probe.Load(ctx).WithError(err).Emit(ProbeDeleteEntityError)
		return nil, fmt.Errorf("delete from entities statement failed: %w", err)
	}
	rowsAffected, err := entitiesRes.RowsAffected()
	if err != nil {
		return nil, fmt.Errorf("failed to get rows affected: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return &dto.ExecutionEntityDeleteResult{
		EntitiesDeleted: rowsAffected,
	}, nil
}
