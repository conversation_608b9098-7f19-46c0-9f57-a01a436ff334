package repo

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
)

func (r Repository) GenerateBranchAndVersionID(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string) {
	branchID, err := r.CreateBranch(ctx, tx, dto.Branch{TenantID: tenant_id, CreatedBy: user_id})
	if err != nil {
		fmt.Printf("Error Branch ID =%s\n", err)
	}
	versionID, err := r.Create<PERSON>ion(ctx, tx, dto.Version{TenantID: tenant_id, ContextID: context_id, BranchID: branchID, CreatedBy: user_id})
	if err != nil {
		fmt.Printf("Error Version ID =%s\n", err)
	}
	return branchID, versionID
}

func (r Repository) DeleteDataModel(ctx context.Context, tenantID string, contextID string, tx *sql.Tx) (*models.DeleteDataModelResponse, error) {

	resRel, err := r.DeleteRelationships(ctx, tx, dto.DeleteRelationshipParams{
		TenantID:  tenantID,
		ContextID: contextID,
	})
	if err != nil {
		return nil, err
	}

	resElements, err := r.DeleteEntityElements(ctx, tx, dto.DeleteElementParams{
		TenantID:  tenantID,
		ContextID: contextID,
	})
	if err != nil {
		return nil, err
	}

	resEntity, err := r.DeleteEntities(ctx, tx, dto.DeleteEntityParams{
		TenantID:  tenantID,
		ContextID: contextID,
	})
	if err != nil {
		return nil, err
	}

	resVersion, err := r.DeleteVersion(ctx, tx, dto.DeleteVersionParams{
		TenantID:  tenantID,
		ContextID: contextID,
	})
	if err != nil {
		return nil, err
	}

	return &models.DeleteDataModelResponse{
		RelationshipsDeleted: resRel.RelationshipsDeleted,
		ElementsDeleted:      resElements.ElementsDeleted,
		EntitiesDeleted:      resEntity.EntitiesDeleted,
		ContextID:            contextID,
		VersionsDeleted:      resVersion.VersionsDeleted,
	}, nil
}
