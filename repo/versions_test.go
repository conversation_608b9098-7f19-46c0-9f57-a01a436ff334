package repo_test

import (
	"context"
	"time"

	dto "github.com/BackOfficeAssoc/catalog/repo/dto"
)

var (
	CreateVersionTestFixtures = map[string]dto.Version{
		"version_1": {
			ID:         "version_1",
			TenantID:   "tenant_1",
			ContextID:  "context_1",
			BranchID:   "branch_1",
			VersionTag: "v1.0",
			ImportID:   "import_1",
			CreatedAt:  time.Now(),
			CreatedBy:  "user_1",
			DeletedAt:  nil,
			DeletedBy:  nil,
		},
	}
)

func (s *Suite) TestCreateVersion() {
	var (
		ctx = context.Background()
	)

	testCases := []struct {
		name string
		err  error
	}{
		{"version_1", nil},
	}

	for _, tt := range testCases {
		s.Run(tt.name, func() {
			req := CreateVersionTestFixtures[tt.name]
			_, err := s.Repository.CreateVersion(ctx, nil, req)
			if err != nil {
				s.T().Errorf("CreateVersion() error = %v", err)
			}
		})
	}
}

func (s *Suite) TestDeleteVersion() {
	var (
		ctx = context.Background()
	)

	testCases := []struct {
		TenantID   string
		ID         string
		BranchID   string
		VersionTag string
		ContextID  string
	}{
		{"tenant_1", "version_1", "branch_1", "v1.0", "context_1"},
	}

	for _, tt := range testCases {
		s.Run(tt.TenantID, func() {
			rowsAffected, err := s.Repository.DeleteVersion(ctx, nil, dto.DeleteVersionParams{
				TenantID:  tt.TenantID,
				ContextID: tt.ContextID,
			})
			if err != nil {
				s.T().Errorf("DeleteVersion() error = %v", err)
			}
			s.NoError(err, "unexpected error from Delete Versions")
			s.Equal(int64(0), *&rowsAffected.VersionsDeleted, "unexpected rowsAffected returned from Delete Versions")

		})
	}
}
