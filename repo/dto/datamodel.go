package dto

import (
	"fmt"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
)

// isNilOrEmpty checks if a string pointer is nil or points to an empty string
func isNilOrEmpty(s *string) bool {
	return s == nil || *s == ""
}

// Reference represents a reference to an entity or entity element
type Reference struct {
	Ref string `db:"ref"` // Changed from $ref to ref
}

// CreateDataModelInput represents the input for creating a data model
type CreateDataModelInput struct {
	MetaSchemaVersion string                 `db:"meta_schema_version"`
	ContextID         string                 `db:"context_id"`
	Namespace         string                 `db:"namespace"`
	CurrentVersion    string                 `db:"current_version"`
	Entities          []Entity               `db:"entities"`
	Relationships     []ExtendedRelationship `db:"relationships"`
}

// FromModel converts a model.Datamodel to dto.CreateDataModelInput
func (dto *CreateDataModelInput) FromModel(m models.DataModel) {
	dto.MetaSchemaVersion = m.MetaSchemaVersion
	dto.Namespace = m.Namespace
	dto.CurrentVersion = m.CurrentVersion

	// Convert entities
	dto.Entities = make([]Entity, len(m.Entities))
	for i, entity := range m.Entities {
		var dtoEntity Entity
		dtoEntity.FromModel(entity)
		dto.Entities[i] = dtoEntity
	}

	// Convert relationships
	dto.Relationships = make([]ExtendedRelationship, len(m.Relationships))
	for i, rel := range m.Relationships {
		var dtoRel ExtendedRelationship
		dtoRel.FromModel(rel)
		dto.Relationships[i] = dtoRel
	}
}

// DataModelResponse represents the response for data model operations
type DataModel struct {
	MetaSchemaVersion string `db:"meta_schema_version"`
	ContextID         string `db:"context_id"`
	Namespace         string `db:"namespace"`
	CurrentVersion    string `db:"current_version"`
	VersionID         string `db:"version_id"`
	CreatedBy         string `db:"created_by"`
	CreatedAt         string `db:"created_at"`
}

// ToModel converts a dto.DataModelResponse to models.DataModelResponse
func (dto DataModel) ToModel() models.DataModel {
	return models.DataModel{
		MetaSchemaVersion: dto.MetaSchemaVersion,
		Namespace:         dto.Namespace,
		CurrentVersion:    dto.CurrentVersion,
		// VersionID:               dto.VersionID,
		// CreatedBy:               dto.CreatedBy,
		// CreatedAt:               dto.CreatedAt,
	}
}

// FindEntitiesQuery represents the parameters for finding entities. and retrieves all fields from the database
type FindEntitiesQuery struct {
	TenantID        string   `db:"tenant_id"`
	ContextID       string   `db:"context_id"`
	Types           []string `db:"entity_type"`
	Filter          *string
	First           *int
	Offset          *string
	Sort            *string
	NameExact       *string `db:"name_exact"`
	NameText        *string `db:"name_text"`
	DescriptionText *string `db:"description_text"`
	IsRootLevel     bool    `db:"-"`
}

// Validate checks if the query is valid
func (q *FindEntitiesQuery) Validate() error {
	if q.TenantID == "" {
		return fmt.Errorf("TenantID is required for finding entities")
	}

	// Convert Types to uppercase for case-insensitive matching
	if len(q.Types) > 0 {
		for i, t := range q.Types {
			q.Types[i] = strings.ToUpper(strings.TrimSpace(t))
		}
	}
	return nil
}

// Where builds the WHERE clause for the SQL query
func (q FindEntitiesQuery) Where() string {
	var conditions []string
	if !q.IsRootLevel && q.ContextID != "" {
		conditions = append(conditions, "context_id = :context_id")
	}
	if len(q.Types) > 0 {
		// Use UPPER on the column for case-insensitive matching
		conditions = append(conditions, "UPPER(entity_type) IN (:entity_type)")
	}
	if !isNilOrEmpty(q.Filter) {
		conditions = append(conditions, "(name ILIKE '%' || :filter || '%' OR description ILIKE '%' || :filter || '%')")
	}
	if !isNilOrEmpty(q.NameExact) {
		conditions = append(conditions, "name = :name_exact")
	}
	if !isNilOrEmpty(q.NameText) {
		conditions = append(conditions, "name ILIKE '%' || :name_text || '%'")
	}
	if !isNilOrEmpty(q.DescriptionText) {
		conditions = append(conditions, "to_tsvector('english', description) @@ plainto_tsquery('english', :description_text)")
	}
	if !isNilOrEmpty(q.Offset) {
		conditions = append(conditions, "id > :offset")
	}
	if len(conditions) == 0 {
		return "WHERE 1=1"
	}
	return "WHERE " + strings.Join(conditions, " AND ")
}

// OrderBy builds the ORDER BY clause for the SQL query
func (q FindEntitiesQuery) OrderBy() string {
	if q.Sort != nil && *q.Sort != "" {
		safeSort := SanitizeSortString(*q.Sort)
		if safeSort != "" {
			return "ORDER BY " + safeSort
		}
	}
	return "ORDER BY name ASC, id ASC" // Default sort
}

// Limit builds the LIMIT clause for the SQL query
func (q FindEntitiesQuery) Limit() string {
	if q.First != nil && *q.First > 0 {
		return fmt.Sprintf("LIMIT %d", *q.First+1)
	}
	return ""
}

// FindEntityElementsQuery represents the parameters for finding entity elements. and also retrieves the elements from the database
type FindEntityElementsQuery struct {
	TenantID        string   `db:"tenant_id"`
	ParentID        string   `db:"parent_id"`
	ContextID       *string  `db:"context_id"`
	Types           []string `db:"element_type"`
	Filter          *string
	First           *int
	Offset          *string
	Sort            *string
	NameExact       *string `db:"name_exact"`
	NameText        *string `db:"name_text"`
	DescriptionText *string `db:"description_text"`
}

// Validate checks if the query is valid
func (q *FindEntityElementsQuery) Validate() error {
	if q.TenantID == "" {
		return fmt.Errorf("TenantID is required for finding entity elements")
	}
	if q.ParentID == "" {
		return fmt.Errorf("ParentID is required for finding entity elements")
	}

	// Convert Types to uppercase for case-insensitive matching
	if len(q.Types) > 0 {
		for i, t := range q.Types {
			q.Types[i] = strings.ToUpper(strings.TrimSpace(t))
		}
	}
	return nil
}

// Where builds the WHERE clause for the SQL query
func (q FindEntityElementsQuery) Where() string {
	var conditions []string
	conditions = append(conditions, "parent_id = :parent_id")

	if q.ContextID != nil && *q.ContextID != "" {
		conditions = append(conditions, "context_id = :context_id")
	}
	if len(q.Types) > 0 {
		conditions = append(conditions, "UPPER(element_type) IN (:element_type)")
	}
	if !isNilOrEmpty(q.Filter) {
		conditions = append(conditions, "(name ILIKE '%' || :filter || '%' OR description ILIKE '%' || :filter || '%')")
	}
	if !isNilOrEmpty(q.NameExact) {
		conditions = append(conditions, "name = :name_exact")
	}
	if !isNilOrEmpty(q.NameText) {
		conditions = append(conditions, "name ILIKE '%' || :name_text || '%'")
	}
	if !isNilOrEmpty(q.DescriptionText) {
		conditions = append(conditions, "to_tsvector('english', description) @@ plainto_tsquery('english', :description_text)")
	}
	if !isNilOrEmpty(q.Offset) {
		conditions = append(conditions, "id > :offset")
	}

	if len(conditions) == 0 {
		return "WHERE 1=1" // Fallback,
	}
	return "WHERE " + strings.Join(conditions, " AND ")
}

// OrderBy builds the ORDER BY clause for the SQL query
func (q FindEntityElementsQuery) OrderBy() string {
	if q.Sort != nil && *q.Sort != "" {
		safeSort := SanitizeSortString(*q.Sort)
		if safeSort != "" {
			return "ORDER BY " + safeSort
		}
	}
	return "ORDER BY name ASC, id ASC"
}

func (q FindEntityElementsQuery) Limit() string {
	if q.First != nil && *q.First > 0 {
		return fmt.Sprintf("LIMIT %d", *q.First+1) // +1 to check for next page
	}
	return "" // No limit
}

// NavigateDataModelQuery represents the DTO for navigating a data model
type NavigateDataModelQuery struct {
	ContextID       string   `json:"context_id,omitempty"`
	ParentID        string   `json:"parent_id,omitempty"`
	Types           []string `json:"types,omitempty"`
	Filter          string   `json:"filter,omitempty"`
	First           int      `json:"first,omitempty"`
	Offset          string   `json:"offset,omitempty"`
	Sort            string   `json:"sort,omitempty"`
	NameExact       string   `json:"name_exact,omitempty"`
	NameText        string   `json:"name_text,omitempty"`
	DescriptionText string   `json:"description_text,omitempty"`
}

// FromModel converts models.NavigateDataModelQuery to dto.NavigateDataModelQuery
func (dto *NavigateDataModelQuery) FromModel(m models.NavigateDataModelQuery) {
	dto.ContextID = m.ContextID
	dto.ParentID = m.ParentID
	dto.Types = m.Types
	dto.Filter = m.Filter
	dto.First = m.First
	dto.Offset = m.Offset
	dto.Sort = m.Sort
	dto.NameExact = m.NameExact
	dto.NameText = m.NameText
	dto.DescriptionText = m.DescriptionText
}

// ToModel converts dto.NavigateDataModelQuery to models.NavigateDataModelQuery
func (dto NavigateDataModelQuery) ToModel() models.NavigateDataModelQuery {
	return models.NavigateDataModelQuery{
		ContextID:       dto.ContextID,
		ParentID:        dto.ParentID,
		Types:           dto.Types,
		Filter:          dto.Filter,
		First:           dto.First,
		Offset:          dto.Offset,
		Sort:            dto.Sort,
		NameExact:       dto.NameExact,
		NameText:        dto.NameText,
		DescriptionText: dto.DescriptionText,
	}
}

// NavigateResult contains the results from NavigateDataModel repository call
type NavigateResult struct {
	Entities       Entities
	EntityElements EntityElements
	Relationships  Relationships
	NextToken      string
}

// ToNavigateResponse converts NavigateResult to NavigateResponse for the API
func (nr *NavigateResult) ToNavigateResponse() NavigateResponse {
	resp := NavigateResponse{
		NextToken: nr.NextToken,
	}

	if len(nr.Entities) > 0 {
		resp.Entities = make([]models.Entity, len(nr.Entities))
		for i, e := range nr.Entities {
			resp.Entities[i] = e.ToModel()
		}
	}

	if len(nr.EntityElements) > 0 {
		resp.EntityElements = make([]models.EntityElement, len(nr.EntityElements))
		for i, ee := range nr.EntityElements {
			resp.EntityElements[i] = *ee.ToModel()
		}
	}

	if len(nr.Relationships) > 0 {
		resp.Relationships = make([]models.Relationship, len(nr.Relationships))
		for i, rel := range nr.Relationships {
			resp.Relationships[i] = rel.ToModel()
		}
	}

	return resp
}

// NavigateResponse represents the response for the navigate API.
type NavigateResponse struct {
	Entities       []models.Entity        `json:"entities,omitempty"`
	EntityElements []models.EntityElement `json:"entity_elements,omitempty"`
	Relationships  []models.Relationship  `json:"relationships,omitempty"`
	NextToken      string                 `json:"next_token,omitempty"`
}
