package dto

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
)

// ExtendedRelationship represents a relationship between entities or entity elements with additional fields for database storage
type ExtendedRelationship struct {
	TenantID         string          `db:"tenant_id"`
	ID               string          `db:"id"`
	MetaVersion      string          `db:"meta_version"`
	ContextID        string          `db:"context_id"`
	Name             string          `db:"name"`
	Type             string          `db:"relation_type"`
	Description      string          `db:"description,omitempty"`
	Active           bool            `db:"active"`
	OrdinalPosition  int             `db:"ordinal_position"`
	Source           Reference       `db:"source"`
	Target           Reference       `db:"target"`
	SourceID         string          `db:"source_id"`
	TargetID         string          `db:"target_id"`
	CreatedAt        string          `db:"created_at"`
	CreatedBy        string          `db:"created_by"`
	VersionID        string          `db:"version_id"`
	BranchID         string          `db:"branch_id"`
	Properties       json.RawMessage `db:"properties"`        // Changed to json.RawMessage
	CustomProperties json.RawMessage `db:"custom_properties"` // Changed to json.RawMessage
}

type Relationships []ExtendedRelationship

// FromModel converts a models.Relationship to dto.ExtendedRelationship
func (r *ExtendedRelationship) FromModel(m models.Relationship) {
	r.Name = m.Name
	r.Type = m.Type
	r.Description = m.Description
	r.OrdinalPosition = m.OrdinalPosition
	r.Active = m.Active
	r.Source = Reference{Ref: m.Source.Ref}
	r.Target = Reference{Ref: m.Target.Ref}
	r.TenantID = m.TenantID
	r.ID = m.ID
	r.MetaVersion = m.MetaVersion
	r.ContextID = m.ContextID
	r.SourceID = m.SourceID
	r.TargetID = m.TargetID
	r.CreatedAt = m.CreatedAt
	r.CreatedBy = m.CreatedBy
	r.VersionID = m.VersionID
	r.BranchID = m.BranchID
	// Marshal map to json.RawMessage for Properties
	if m.Properties != nil {
		propBytes, err := json.Marshal(m.Properties)
		if err == nil { // Handle error appropriately in real code
			r.Properties = propBytes
		}
	}
	// Marshal map to json.RawMessage for CustomProperties
	if m.CustomProperties != nil {
		customPropBytes, err := json.Marshal(m.CustomProperties)
		if err == nil { // Handle error appropriately in real code
			r.CustomProperties = customPropBytes
		}
	}
}

// ToModel converts a dto.ExtendedRelationship to models.Relationship
func (r ExtendedRelationship) ToModel() models.Relationship {
	modelRel := models.Relationship{
		Name:        r.Name,
		Type:        r.Type,
		Description: r.Description,
		Active:      r.Active,
		Source:      models.Reference{Ref: r.Source.Ref},
		Target:      models.Reference{Ref: r.Target.Ref},
		TenantID:    r.TenantID,
		ID:          r.ID,
		MetaVersion: r.MetaVersion,
		ContextID:   r.ContextID,
		SourceID:    r.SourceID,
		TargetID:    r.TargetID,
		CreatedAt:   r.CreatedAt,
		CreatedBy:   r.CreatedBy,
		VersionID:   r.VersionID,
		BranchID:    r.BranchID,
		// Properties and CustomProperties will be unmarshalled from json.RawMessage
	}
	if r.Properties != nil {
		if err := json.Unmarshal(r.Properties, &modelRel.Properties); err != nil {
			log.Printf("Error unmarshalling Properties for relationship ID %s: %v", r.ID, err)
			modelRel.Properties = make(map[string]interface{}) // Or some error indication
		}
	}
	if r.CustomProperties != nil {
		if err := json.Unmarshal(r.CustomProperties, &modelRel.CustomProperties); err != nil {
			log.Printf("Error unmarshalling CustomProperties for relationship ID %s: %v", r.ID, err)
			modelRel.CustomProperties = make(map[string]interface{})
		}
	}
	return modelRel
}

// FindRelationshipQuery represents a query to find relationships
type FindRelationshipQuery struct {
	TenantID    string   `form:"tenant_id" url:"tenant_id,omitempty" db:"tenant_id"`
	ID          *string  `form:"id" url:"id,omitempty" db:"id"`
	ContextID   *string  `form:"context_id" url:"context_id,omitempty" db:"context_id"`
	Types       []string `form:"type" url:"type,omitempty" db:"relation_type"`
	SourceRef   *string  `form:"source_ref" url:"source_ref,omitempty" db:"source_ref"`       // For exact match on source_ref
	TargetRef   *string  `form:"target_ref" url:"target_ref,omitempty" db:"target_ref"`       // For exact match on target_ref
	RelatedRefs []string `form:"related_refs" url:"related_refs,omitempty" db:"related_refs"` // For source_ref IN (...) OR target_ref IN (...)
	CreatedBy   *string  `form:"created_by" url:"created_by,omitempty" db:"created_by"`

	Offset *string `form:"offset" url:"offset,omitempty"`
	First  *int    `form:"first" url:"first,omitempty"`
	Filter string  `form:"filter" url:"filter,omitempty"`
	Sort   string  `form:"sort" url:"sort,omitempty"`
}

// Validate checks if the query is valid
func (q FindRelationshipQuery) Validate() error {
	if q.TenantID == "" {
		return fmt.Errorf("tenant_id is required")
	}
	return nil
}

// Where builds the WHERE clause for the SQL query
func (q FindRelationshipQuery) Where() string {
	var conditions []string

	if q.TenantID != "" {
		conditions = append(conditions, "tenant_id = :tenant_id")
	}
	if q.ID != nil {
		conditions = append(conditions, "id = :id")
	}
	if q.ContextID != nil && *q.ContextID != "" {
		conditions = append(conditions, "context_id = :context_id")
	}
	if len(q.Types) > 0 {
		conditions = append(conditions, "relation_type IN (:relation_type)")
	}
	if q.SourceRef != nil { // For single, exact source_ref match
		conditions = append(conditions, "source_ref = :source_ref")
	}
	if q.TargetRef != nil { // For single, exact target_ref match
		conditions = append(conditions, "target_ref = :target_ref")
	}
	if len(q.RelatedRefs) > 0 {
		// This will construct a clause like: (source_ref IN (:related_refs) OR target_ref IN (:related_refs))
		// sqlx should expand :related_refs for the IN clause.
		conditions = append(conditions, "(source_ref IN (:related_refs) OR target_ref IN (:related_refs))")
	}
	if q.CreatedBy != nil {
		conditions = append(conditions, "created_by = :created_by")
	}
	if q.Offset != nil {
		conditions = append(conditions, "id > :offset")
	}

	if len(conditions) == 0 {
		return ""
	}
	return "WHERE " + strings.Join(conditions, " AND ")
}

// OrderBy builds the ORDER BY clause for the SQL query
func (q FindRelationshipQuery) OrderBy() string {
	if q.Sort != "" {
		safeSort := SanitizeSortString(q.Sort)
		if safeSort != "" {
			return "ORDER BY " + safeSort
		}
	}
	return "ORDER BY name ASC, id ASC"
}

func SanitizeSortString(sort string) string {
	allowedColumns := map[string]bool{
		"name": true, "id": true, "created_at": true, "ordinal_position": true,
		"element_type": true, "entity_type": true, "relation_type": true,
	}

	parts := strings.Split(sort, ",")
	var validParts []string

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		// Check for DESC suffix based on the upcoming task to filter by desc?
		desc := false
		if strings.HasSuffix(strings.ToUpper(part), " DESC") {
			desc = true
			part = strings.TrimSuffix(strings.TrimSpace(part[:len(part)-4]), " ")
		} else if strings.HasSuffix(strings.ToUpper(part), " ASC") {
			part = strings.TrimSuffix(strings.TrimSpace(part[:len(part)-3]), " ")
		}

		// Check if column is allowed
		if allowedColumns[part] {
			if desc {
				validParts = append(validParts, part+" DESC")
			} else {
				validParts = append(validParts, part+" ASC")
			}
		}
	}

	if len(validParts) == 0 {
		return "name ASC" // Default sort
	}

	return strings.Join(validParts, ", ")
}

// Limit builds the LIMIT clause for the SQL query
func (q FindRelationshipQuery) Limit() string {
	if q.First != nil {
		return fmt.Sprintf("LIMIT %d", *q.First+1)
	}
	return ""
}

// DeleteRelationshipParams represents parameters for deleting a relationship
type DeleteRelationshipParams struct {
	TenantID  string `json:"tenant_id"`
	ContextID string `json:"context_id"`
}

// ExecutionRelationshipDeleteResult represents the result of a delete operation
type ExecutionRelationshipDeleteResult struct {
	RelationshipsDeleted int64 `json:"relationships_deleted"`
}

// FindRelationshipParams is now part of FindRelationshipQuery.
