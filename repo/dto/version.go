package dto

import (
	"time"

	"github.com/BackOfficeAssoc/catalog/models"
)

type Version struct {
	ID          string     `db:"id"`
	TenantID    string     `db:"tenant_id"`
	ContextID   string     `db:"context_id"`
	BranchID    string     `db:"branch_id"`
	VersionTag  string     `db:"version_tag"`
	StartedAt   time.Time  `db:"started_at"`
	CompletedAt time.Time  `db:"completed_at"`
	ImportID    string     `db:"import_id"`
	CreatedAt   time.Time  `db:"created_at"`
	CreatedBy   string     `db:"created_by"`
	DeletedAt   *time.Time `db:"deleted_at,omitempty"`
	DeletedBy   *string    `db:"deleted_by,omitempty"`
}

type Versions []Version

func (v *Version) FromModel(m models.Version) {
	v.ID = m.ID
	v.TenantID = m.TenantID
	v.ContextID = m.ContextID
	v.BranchID = m.BranchID
	v.VersionTag = m.VersionTag
	v.StartedAt = m.StartedAt
	v.CompletedAt = m.CompletedAt
	v.ImportID = m.ImportID
	v.CreatedAt = m.CreatedAt
	v.CreatedBy = m.CreatedBy
	v.DeletedAt = m.DeletedAt
	v.DeletedBy = m.DeletedBy
}
func (v *Version) ToModel() models.Version {
	return models.Version{
		ID:          v.ID,
		TenantID:    v.TenantID,
		ContextID:   v.ContextID,
		BranchID:    v.BranchID,
		VersionTag:  v.VersionTag,
		StartedAt:   v.StartedAt,
		CompletedAt: v.CompletedAt,
		ImportID:    v.ImportID,
		CreatedAt:   v.CreatedAt,
		CreatedBy:   v.CreatedBy,
		DeletedAt:   v.DeletedAt,
		DeletedBy:   v.DeletedBy,
	}
}

type FindVersionQuery struct {
	TenantID   *string `db:"tenant_id"`
	BranchID   *string `db:"branch_id"`
	ContextID  *string `db:"context_id"`
	ID         *string `db:"id"`
	VersionTag *string `db:"version_tag"`

	After *time.Time `db:"after"`
	First *int       `db:"first"`
}

func (q *FindVersionQuery) FromModel(m models.FindVersionQuery) error {
	if m.After != nil {
		after, err := cursorToTime(m.After)
		if err != nil {
			return err
		}
		q.After = after
	}
	q.TenantID = m.TenantID
	q.VersionTag = m.VersionTag
	q.ID = m.ID
	q.First = m.First
	return q.Validate()
}
func (q *FindVersionQuery) Validate() error {
	if q.TenantID == nil {
		return ErrNoTenant
	}
	if q.BranchID == nil {
		return ErrNoBranch
	}
	return nil
}
func (q FindVersionQuery) Limit() string {
	if q.First == nil {
		return ""
	}
	return "LIMIT :first "
}
func (q FindVersionQuery) OrderBy() string {
	return "ORDER BY created_at DESC "
}
func (q FindVersionQuery) Where() string {
	where := "WHERE 1=1 "
	where += q.tenantFilter()
	where += q.afterFilter()
	where += q.idFilter()
	where += q.branchFilter()
	where += q.contextFilter()
	where += q.versionTagFilter()

	return where
}

func (q FindVersionQuery) branchFilter() string {
	if q.BranchID == nil {
		return ""
	}
	return "AND branch_id = :branch_id "
}
func (q FindVersionQuery) afterFilter() string {
	if q.After == nil {
		return ""
	}
	return "AND created_at < :after "
}
func (q FindVersionQuery) tenantFilter() string {
	if q.TenantID == nil {
		return ""
	}
	return "AND tenant_id = :tenant_id "
}
func (q FindVersionQuery) idFilter() string {
	if q.ID == nil {
		return ""
	}
	return "AND id = :id "
}
func (q FindVersionQuery) versionTagFilter() string {
	if q.VersionTag == nil {
		return ""
	}
	return "AND version_tag = :version_tag "
}
func (q FindVersionQuery) contextFilter() string {
	if q.TenantID == nil {
		return ""
	}
	return "AND context_id = :context_id "
}

type DeleteVersionParams struct {
	TenantID  string `db:"tenant_id"`
	ContextID string `db:"context_id"`
}
type ExecutionVersionDeleteResult struct {
	VersionsDeleted int64
}
