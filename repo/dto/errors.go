package dto

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
)

// ErrorDetail represents individual error information
type ErrorDetail struct {
	Field   string `json:"field,omitempty"`
	Message string `json:"message"`
}

// FromModel converts a models.ErrorDetail to dto.ErrorDetail
func (e *ErrorDetail) FromModel(m models.ErrorDetail) {
	e.Field = m.Field
	e.Message = m.Message
}

// ToModel converts a dto.ErrorDetail to models.ErrorDetail
func (e ErrorDetail) ToModel() models.ErrorDetail {
	return models.ErrorDetail{
		Field:   e.Field,
		Message: e.Message,
	}
}

// CatalogError represents the API error response
type CatalogError struct {
	TraceID string        `json:"trace_id"`
	Type    string        `json:"type"`
	Status  int           `json:"status,omitempty"`
	Title   string        `json:"title"`
	Detail  string        `json:"detail,omitempty"`
	Message string        `json:"message,omitempty"`
	Errors  []ErrorDetail `json:"errors,omitempty"`
}

// FromModel converts a models.CatalogError to dto.CatalogError
func (e *CatalogError) FromModel(m models.CatalogError) {
	e.TraceID = m.TraceID
	e.Type = m.Type
	e.Status = m.Status
	e.Title = m.Title
	e.Detail = m.Detail
	e.Message = m.Message

	// Convert error details
	e.Errors = make([]ErrorDetail, len(m.Errors))
	for i, detail := range m.Errors {
		e.Errors[i].FromModel(detail)
	}
}

// ToModel converts a dto.CatalogError to models.CatalogError
func (e CatalogError) ToModel() models.CatalogError {
	model := models.CatalogError{
		TraceID: e.TraceID,
		Type:    e.Type,
		Status:  e.Status,
		Title:   e.Title,
		Detail:  e.Detail,
		Message: e.Message,
	}

	// Convert error details
	model.Errors = make([]models.ErrorDetail, len(e.Errors))
	for i, detail := range e.Errors {
		model.Errors[i] = detail.ToModel()
	}

	return model
}

// ErrorCreationInput represents the input needed to create an error
type ErrorCreationInput struct {
	TraceID           string
	ErrorType         string
	Status            int
	Title             string
	Detail            string
	Message           string
	ErrorDetails      []ErrorDetail
	Resource          string   // For conflict errors
	ProvidedVersion   string   // For unsupported meta schema errors
	SupportedVersions []string // For unsupported meta schema errors
}

// ErrorCreationResult represents the result of creating an error
type ErrorCreationResult struct {
	Error CatalogError
}

// ToModel converts ErrorCreationResult to models.CatalogError
func (r ErrorCreationResult) ToModel() models.CatalogError {
	return r.Error.ToModel()
}

// CreateValidationError creates a validation error
func CreateValidationError(input ErrorCreationInput) ErrorCreationResult {
	var detailMessages []string
	for _, detail := range input.ErrorDetails {
		if detail.Field != "" {
			detailMessages = append(detailMessages, fmt.Sprintf("%s: [%s]", detail.Field, detail.Message))
		} else {
			detailMessages = append(detailMessages, detail.Message)
		}
	}

	detailText := fmt.Sprintf("One or more validation errors occurred. Errors: %s", strings.Join(detailMessages, ", "))

	catalogError := CatalogError{
		TraceID: input.TraceID,
		Type:    models.ErrorTypeValidation,
		Status:  http.StatusBadRequest,
		Title:   models.ErrorTitleValidation,
		Detail:  detailText,
		Message: detailText,
		Errors:  input.ErrorDetails,
	}

	return ErrorCreationResult{Error: catalogError}
}

// CreateUnsupportedMetaSchemaError creates an unsupported meta schema error
func CreateUnsupportedMetaSchemaError(input ErrorCreationInput) ErrorCreationResult {
	detail := fmt.Sprintf("The provided meta-schema version '%s' is not supported by the service.", input.ProvidedVersion)
	if len(input.SupportedVersions) > 0 {
		detail += fmt.Sprintf(" Supported versions: %s", strings.Join(input.SupportedVersions, ", "))
	}

	catalogError := CatalogError{
		TraceID: input.TraceID,
		Type:    models.ErrorTypeUnsupportedMetaSchema,
		Status:  http.StatusBadRequest,
		Title:   models.ErrorTitleUnsupportedMetaSchema,
		Detail:  detail,
		Message: detail,
	}

	return ErrorCreationResult{Error: catalogError}
}

// CreateConflictError creates a conflict error
func CreateConflictError(input ErrorCreationInput) ErrorCreationResult {
	detailText := fmt.Sprintf("The %s already exists. %s", input.Resource, input.Detail)

	catalogError := CatalogError{
		TraceID: input.TraceID,
		Type:    models.ErrorTypeConflict,
		Status:  http.StatusConflict,
		Title:   models.ErrorTitleConflict,
		Detail:  detailText,
		Message: detailText,
	}

	return ErrorCreationResult{Error: catalogError}
}

// CreateInternalServerError creates an internal server error
func CreateInternalServerError(input ErrorCreationInput) ErrorCreationResult {
	catalogError := CatalogError{
		TraceID: input.TraceID,
		Type:    models.ErrorTypeInternalServer,
		Status:  http.StatusInternalServerError,
		Title:   models.ErrorTitleInternalServer,
		Detail:  input.Detail,
		Message: input.Detail,
	}

	return ErrorCreationResult{Error: catalogError}
}

// CreateUnauthorizedError creates an unauthorized error
func CreateUnauthorizedError(input ErrorCreationInput) ErrorCreationResult {
	detail := input.Detail
	if detail == "" {
		detail = "Authentication information is expired, missing, or invalid."
	}

	catalogError := CatalogError{
		TraceID: input.TraceID,
		Type:    models.ErrorTypeUnauthorized,
		Status:  http.StatusUnauthorized,
		Title:   models.ErrorTitleUnauthorized,
		Detail:  detail,
		Message: detail,
	}

	return ErrorCreationResult{Error: catalogError}
}
