package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/pkg/crunch"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/jmoiron/sqlx" // Added sqlx import
)

const (
	sqlFindRelationships = `
	SELECT tenant_id, id, meta_version, context_id, name, relation_type, description, active, ordinal_position,
	 source_ref as "source.ref", target_ref as "target.ref",
	 source_id, target_id, created_at, created_by, version_id, branch_id,
	 properties::text as properties_json, custom_properties::text as custom_properties_json
	 FROM %[1]s
		%[2]s --where
		%[3]s --order by
		%[4]s --limit
	`

	sqlInsertRelationships = `
    INSERT INTO %[1]s (tenant_id, id, meta_version, context_id, name, relation_type, description, active, ordinal_position,
	 source_ref, target_ref, source_id, target_id, created_at, created_by, version_id, branch_id,
	 properties, custom_properties)
	VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, DEFAULT, $14, $15, $16, $17, $18)
     RETURNING id, created_at
  	`

	sqlDeleteRelationships = `DELETE FROM %[1]s WHERE tenant_id = $1 AND context_id = $2`
)

func getRelationshipsTableName(tenantID string) string {
	return fmt.Sprintf("%s.relationships_%s", Schema, tenantID)
}

// FindRelationships retrieves relationships from the database based on the provided query
func (r *Repository) FindRelationships(ctx context.Context, query dto.FindRelationshipQuery) (dto.Relationships, error) {
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("query validation failed: %w", err)
	}

	// Temporary structure to fix some scanning issues on JSON fields
	type TempRelationshipScan struct {
		TenantID             string         `db:"tenant_id"`
		ID                   string         `db:"id"`
		MetaVersion          string         `db:"meta_version"`
		ContextID            string         `db:"context_id"`
		Name                 string         `db:"name"`
		Type                 string         `db:"relation_type"`
		Description          string         `db:"description"`
		Active               bool           `db:"active"`
		OrdinalPosition      int            `db:"ordinal_position"`
		Source               dto.Reference  `db:"source"`
		Target               dto.Reference  `db:"target"`
		SourceID             string         `db:"source_id"`
		TargetID             string         `db:"target_id"`
		CreatedAt            string         `db:"created_at"`
		CreatedBy            string         `db:"created_by"`
		VersionID            string         `db:"version_id"`
		BranchID             string         `db:"branch_id"`
		PropertiesJSON       sql.NullString `db:"properties_json"`        // Scan as string
		CustomPropertiesJSON sql.NullString `db:"custom_properties_json"` // Scan as string
	}

	var tempRels []TempRelationshipScan

	sqlParts := []string{`SELECT tenant_id, id, meta_version, context_id, name, relation_type, description, active, ordinal_position,
	 source_ref as "source.ref", target_ref as "target.ref",
	 source_id, target_id, created_at, created_by, version_id, branch_id,
	 properties::text as properties_json, custom_properties::text as custom_properties_json
	 FROM ` + getRelationshipsTableName(query.TenantID)}

	args := []interface{}{}
	var whereConditions []string

	if query.TenantID != "" {
		whereConditions = append(whereConditions, "tenant_id = ?")
		args = append(args, query.TenantID)
	}
	if query.ID != nil {
		whereConditions = append(whereConditions, "id = ?")
		args = append(args, *query.ID)
	}
	if query.ContextID != nil && *query.ContextID != "" {
		whereConditions = append(whereConditions, "context_id = ?")
		args = append(args, *query.ContextID)
	}
	if query.SourceRef != nil {
		whereConditions = append(whereConditions, "source_ref = ?")
		args = append(args, *query.SourceRef)
	}
	if query.TargetRef != nil {
		whereConditions = append(whereConditions, "target_ref = ?")
		args = append(args, *query.TargetRef)
	}
	if query.CreatedBy != nil {
		whereConditions = append(whereConditions, "created_by = ?")
		args = append(args, *query.CreatedBy)
	}

	// Handle IN clauses for slices
	if len(query.Types) > 0 {
		inQueryPart, inArgs, err := sqlx.In("relation_type IN (?)", query.Types)
		if err != nil {
			return nil, fmt.Errorf("failed to build IN clause for types: %w", err)
		}
		whereConditions = append(whereConditions, inQueryPart)
		args = append(args, inArgs...)
	}

	if len(query.RelatedRefs) > 0 {
		// Source Refs
		sourceInQuery, sourceInArgs, err := sqlx.In("source_ref IN (?)", query.RelatedRefs)
		if err != nil {
			return nil, fmt.Errorf("failed to build IN clause for source_refs: %w", err)
		}

		// Target Refs
		targetInQuery, targetInArgs, err := sqlx.In("target_ref IN (?)", query.RelatedRefs)
		if err != nil {
			return nil, fmt.Errorf("failed to build IN clause for target_refs: %w", err)
		}

		// Combine with OR
		whereConditions = append(whereConditions, fmt.Sprintf("(%s OR %s)", sourceInQuery, targetInQuery))
		args = append(args, sourceInArgs...)
		args = append(args, targetInArgs...)
	}

	if query.Offset != nil && *query.Offset != "" {
		whereConditions = append(whereConditions, "id > ?")
		args = append(args, *query.Offset)
	}

	if len(whereConditions) > 0 {
		sqlParts = append(sqlParts, "WHERE "+strings.Join(whereConditions, " AND "))
	}

	sqlParts = append(sqlParts, query.OrderBy())
	sqlParts = append(sqlParts, query.Limit())

	finalQuery := strings.Join(sqlParts, " ")

	// Rebind the query to use PostgreSQL's $1, $2, ... placeholders
	reboundQuery := r.DB.Rebind(finalQuery)

	err := r.DB.SelectContext(ctx, &tempRels, reboundQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("find relationships statement failed (SelectContext): %w\nQuery: %s\nArgs: %v", err, reboundQuery, args)
	}

	var relationships dto.Relationships
	for _, tr := range tempRels {
		rel := dto.ExtendedRelationship{
			TenantID:        tr.TenantID,
			ID:              tr.ID,
			MetaVersion:     tr.MetaVersion,
			ContextID:       tr.ContextID,
			Name:            tr.Name,
			Type:            tr.Type,
			Description:     tr.Description,
			Active:          tr.Active,
			OrdinalPosition: tr.OrdinalPosition,
			Source:          tr.Source,
			Target:          tr.Target,
			SourceID:        tr.SourceID,
			TargetID:        tr.TargetID,
			CreatedAt:       tr.CreatedAt,
			CreatedBy:       tr.CreatedBy,
			VersionID:       tr.VersionID,
			BranchID:        tr.BranchID,
		}
		if tr.PropertiesJSON.Valid && tr.PropertiesJSON.String != "" {
			rel.Properties = json.RawMessage(tr.PropertiesJSON.String)
		}
		if tr.CustomPropertiesJSON.Valid && tr.CustomPropertiesJSON.String != "" {
			rel.CustomProperties = json.RawMessage(tr.CustomPropertiesJSON.String)
		}
		relationships = append(relationships, rel)
	}

	return relationships, nil
}

// CreateRelationships inserts relationships into the database
func (r *Repository) CreateRelationships(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error {
	var err error
	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return fmt.Errorf("failed to begin transaction: %w", err)
		}
	}

	defer tx.Rollback()

	// Prepare batch insert
	sqlInsertStmt := fmt.Sprintf(sqlInsertRelationships, getRelationshipsTableName(relationships[0].TenantID))
	stmt, err := tx.PrepareContext(ctx, sqlInsertStmt)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	for i := range relationships {
		id, err := crunch.Generate("rel")
		if err != nil {
			return fmt.Errorf("failed to generate ID: %w", err)
		}
		var CreatedAt string
		// Convert Properties and CustomProperties to JSON
		propertiesJSON, err := json.Marshal(relationships[i].Properties)
		if err != nil {
			return fmt.Errorf("failed to marshal properties to JSON: %w", err)
		}

		customPropertiesJSON, err := json.Marshal(relationships[i].CustomProperties)
		if err != nil {
			return fmt.Errorf("failed to marshal custom properties to JSON: %w", err)
		}

		// Let the database handle the timestamp with DEFAULT NOW() in the SQL statement
		err = stmt.QueryRowContext(ctx,
			relationships[i].TenantID,
			id, // Use generated ID instead of relationships[i].ID
			relationships[i].MetaVersion,
			relationships[i].ContextID,
			relationships[i].Name,
			relationships[i].Type,
			relationships[i].Description,
			relationships[i].Active,
			relationships[i].OrdinalPosition,
			relationships[i].Source.Ref,
			relationships[i].Target.Ref,
			relationships[i].SourceID,
			relationships[i].TargetID,
			// created_at is handled by DEFAULT in the SQL statement
			relationships[i].CreatedBy,
			relationships[i].VersionID,
			relationships[i].BranchID,
			propertiesJSON,       // Use JSON instead of map
			customPropertiesJSON, // Use JSON instead of map
		).Scan(&id, &CreatedAt)
		if err != nil {
			probe.Load(ctx).WithError(err).Emit(ProbeCreateRelationshipError)
			return fmt.Errorf("failed to insert relationship: %w", err)
		}
		relationships[i].ID = id
		relationships[i].CreatedAt = CreatedAt
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// DeleteRelationships deletes relationships from the database
func (r *Repository) DeleteRelationships(ctx context.Context, tx *sql.Tx, params dto.DeleteRelationshipParams) (*dto.ExecutionRelationshipDeleteResult, error) {
	var err error
	if tx == nil {
		tx, err = r.DB.BeginTx(ctx, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to begin transaction: %w", err)
		}
	}
	defer tx.Rollback()

	sqlDeleteStmt := fmt.Sprintf(sqlDeleteRelationships, getRelationshipsTableName(params.TenantID))
	relationshipsRes, err := tx.ExecContext(ctx, sqlDeleteStmt, params.TenantID, params.ContextID)
	if err != nil {
		probe.Load(ctx).WithError(err).Emit(ProbeDeleteRelationshipError)
		return nil, fmt.Errorf("delete from relationships statement failed: %w", err)
	}
	rowsAffected, err := relationshipsRes.RowsAffected()
	if err != nil {
		return nil, fmt.Errorf("failed to get rows affected: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return &dto.ExecutionRelationshipDeleteResult{
		RelationshipsDeleted: rowsAffected,
	}, nil
}
