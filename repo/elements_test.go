package repo_test

import (
	"context"

	dto "github.com/BackOfficeAssoc/catalog/repo/dto"
)

// Helper function to create a PropertiesMap pointer
func propsPtr(m map[string]interface{}) *dto.PropertiesMap {
	props := dto.PropertiesMap(m)
	return &props
}

var (
	CreateElementTestFixtures = map[string]dto.EntityElement{
		"element_1": {
			Name:             "Element 1",
			ModelName:        "n1.entity1.element1",
			Type:             "COLUMN",
			ConsumptionType:  "field",
			Description:      "Element 1 description",
			Active:           true,
			DataType:         "int",
			IsRequired:       true,
			IsArray:          false,
			Properties:       propsPtr(map[string]interface{}{}),
			CustomProperties: propsPtr(map[string]interface{}{}),
			Tags:             []string{"tag1", "tag2"},
			CreatedAt:        "2025-04-07 12:38:27.191 +0530",
			ContextID:        "ctx_333_a",
			TenantID:         "tnt_111",
			ID:               "elm_333",
			MetaVersion:      "meta_1",
			OrdinalPosition:  1,
			Ref:              "ref_1",
			MappedElement:    "mapped_1",
			CreatedBy:        "user_333_a",
			VersionID:        "version_333_a",
			BranchID:         "branch_111_a",
			ParentID:         "ent_111",
		},
		"primary_key_1": {
			Name:             "PK_Element_1",
			ModelName:        "n1.entity1.pk.element1",
			Type:             "PRIMARY-KEY",
			ConsumptionType:  "key",
			Description:      "Primary key for Element 1",
			Active:           true,
			Properties:       propsPtr(map[string]interface{}{}),
			CustomProperties: propsPtr(map[string]interface{}{}),
			Tags:             []string{"tag1", "tag2"},
			CreatedAt:        "2025-04-07 12:38:27.191 +0530",
			ContextID:        "ctx_333_a",
			TenantID:         "tnt_111",
			ID:               "elm_334",
			MetaVersion:      "meta_1",
			OrdinalPosition:  1,
			Ref:              "ref_1",
			MappedElement:    "mapped_1",
			CreatedBy:        "user_333_a",
			VersionID:        "version_333_a",
			BranchID:         "branch_111_a",
			ParentID:         "ent_111",
		},
		"index_1": {
			Name:            "IDX_Element_1",
			ModelName:       "n1.entity1.idx.element1",
			Type:            "INDEX",
			ConsumptionType: "index",
			Description:     "Index for Element 1",
			Active:          true,
			Properties: propsPtr(map[string]interface{}{
				"is_clustered": true,
				"is_unique":    true,
			}),
			CustomProperties: propsPtr(map[string]interface{}{}),
			Tags:             []string{"tag1", "tag2"},
			CreatedAt:        "2025-04-07 12:38:27.191 +0530",
			ContextID:        "ctx_333_a",
			TenantID:         "tnt_111",
			ID:               "elm_335",
			MetaVersion:      "meta_1",
			OrdinalPosition:  1,
			Ref:              "ref_1",
			MappedElement:    "mapped_1",
			CreatedBy:        "user_333_a",
			VersionID:        "version_333_a",
			BranchID:         "branch_111_a",
			ParentID:         "ent_111",
		},
		"foreign_key_1": {
			Name:            "FK_Element_1",
			ModelName:       "n1.entity1.fk.element1",
			Type:            "FOREIGN-KEY",
			ConsumptionType: "constraint",
			Description:     "Foreign key for Element 1",
			Active:          true,
			Properties: propsPtr(map[string]interface{}{
				"on_update": "CASCADE",
				"on_delete": "NO ACTION",
				"validate":  true,
			}),
			CustomProperties: propsPtr(map[string]interface{}{}),
			Tags:             []string{"tag1", "tag2"},
			CreatedAt:        "2025-04-07 12:38:27.191 +0530",
			ContextID:        "ctx_333_a",
			TenantID:         "tnt_111",
			ID:               "elm_336",
			MetaVersion:      "meta_1",
			OrdinalPosition:  1,
			Ref:              "ref_1",
			MappedElement:    "mapped_1",
			CreatedBy:        "user_333_a",
			VersionID:        "version_333_a",
			BranchID:         "branch_111_a",
			ParentID:         "ent_111",
		},
	}
)

func (s *Suite) TestCreateEntityElement() {
	var (
		ctx = context.Background()
	)

	// Skip this test for now as it requires a mock for the ID generator
	s.T().Skip("Skipping test as it requires a mock for the ID generator")

	testCases := []struct {
		name     string
		parentID string
		err      error
	}{
		{"element_1", "ent_111", nil},
		{"primary_key_1", "ent_111", nil},
		{"index_1", "ent_111", nil},
		{"foreign_key_1", "ent_111", nil},
	}

	for _, tt := range testCases {
		s.Run(tt.name, func() {
			req := CreateElementTestFixtures[tt.name]
			var createEntityElements dto.EntityElements
			createEntityElements = append(createEntityElements, req)

			err := s.Repository.CreateEntityElements(ctx, nil, tt.parentID, createEntityElements)
			s.Require().ErrorIs(err, tt.err)
			if tt.err != nil {
				return // to the next test
			}
			s.NoError(err, "create resulted in an unexpected error")
			filterName := req.Name
			findQuery := dto.FindEntityElementsQuery{
				TenantID: req.TenantID,
				ParentID: req.ParentID,
				Filter:   &filterName,
			}
			entityElements, err := s.Repository.FindEntityElements(ctx, findQuery)

			s.NoError(err, "find resulted in an unexpected error")
			s.Len(entityElements, 1, "find resulted in unexpected number of results")
		})
	}
}

func (s *Suite) TestDeleteEntityElements() {
	var (
		ctx = context.Background()
	)

	testCases := []struct {
		TenantID  string
		ContextID string
	}{
		{"tnt_111", "ctx_333_a"},
		{"tnt_111", "ctx_333_a"}, // PRIMARY-KEY
		{"tnt_111", "ctx_333_a"}, // INDEX
		{"tnt_111", "ctx_333_a"}, // FOREIGN-KEY
	}

	for _, tt := range testCases {
		s.Run(tt.TenantID, func() {

			deleteElementParams := dto.DeleteElementParams{
				TenantID:  tt.TenantID,
				ContextID: tt.ContextID}

			rowsAffected, err := s.Repository.DeleteEntityElements(ctx, nil, deleteElementParams)
			s.NoError(err, "unexpected error from Delete Entity Element")
			s.Equal(int64(0), *&rowsAffected.ElementsDeleted, "unexpected rowsAffected returned from Delete Entity Element")
		})
	}
}
