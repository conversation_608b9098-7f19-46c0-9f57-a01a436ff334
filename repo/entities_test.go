package repo_test

import (
	"context"

	dto "github.com/BackOfficeAssoc/catalog/repo/dto"
)

// Helper function to create a string pointer
func stringPtr(s string) *string {
	return &s
}

var (
	CreateTestFixtures = map[string]dto.Entity{
		"entity_1": {
			Name:             "Entity 1",
			ModelName:        "n1.entity1",
			Type:             "Foo",
			Description:      "Entity 1 description",
			Active:           true,
			Properties:       (*dto.PropertiesMap)(&map[string]interface{}{}),
			CustomProperties: (*dto.PropertiesMap)(&map[string]interface{}{}),
			Tags:             []string{"tag1", "tag2"},
			CreatedAt:        "2025-04-07 12:38:27.191 +0530",
			ContextID:        "ctx_333_a",
			TenantID:         "tnt_111",
			ID:               "ent_test_12345",
			MetaVersion:      "meta_1",
			OrdinalPosition:  1,
			BaseType:         "base_1",
			Ref:              "ref_1",
			MappedEntity:     stringPtr("mapped_1"),
			CreatedBy:        "user_333_a",
			VersionID:        "version_333_a",
			BranchID:         "branch_111_a",
			EntityElements:   []dto.EntityElement{},
		},
	}
)

func (s *Suite) TestCreateEntity() {
	var (
		ctx = context.Background()
	)

	// Skip this test for now as it requires a mock for the ID generator
	s.T().Skip("Skipping test as it requires a mock for the ID generator")

	testCases := []struct {
		name string
		err  error
	}{
		{"entity_1", nil},
	}

	for _, tt := range testCases {
		s.Run(tt.name, func() {
			req := CreateTestFixtures[tt.name]
			var createEntities dto.Entities
			createEntities = append(createEntities, req)

			err := s.Repository.CreateEntities(ctx, nil, createEntities)
			s.Require().ErrorIs(err, tt.err)
			if tt.err != nil {
				return // to the next test
			}
			s.NoError(err, "create resulted in an unexpected error")
			filterName := req.Name
			findQuery := dto.FindEntitiesQuery{
				TenantID:  req.TenantID,
				ContextID: req.ContextID,
				Filter:    &filterName,
			}
			entities, err := s.Repository.FindEntities(ctx, findQuery)

			s.NoError(err, "find resulted in an unexpected error")
			s.Len(entities, 1, "find resulted in unexpected number of results")
		})
	}
}

func (s *Suite) TestDeteteEntities() {
	var (
		ctx = context.Background()
	)

	testCases := []struct {
		TenantID  string
		ContextID string
	}{
		{"tnt_111", "ctx_333_a"},
	}

	for _, tt := range testCases {
		s.Run(tt.TenantID, func() {

			deleteEntityParams := dto.DeleteEntityParams{
				TenantID:  tt.TenantID,
				ContextID: tt.ContextID}

			rowsAffected, err := s.Repository.DeleteEntities(ctx, nil, deleteEntityParams)
			s.NoError(err, "unexpected error from Delete Entity")
			s.Equal(int64(0), *&rowsAffected.EntitiesDeleted, "unexpected rowsAffected returned from Delete Entity")
		})
	}
}
