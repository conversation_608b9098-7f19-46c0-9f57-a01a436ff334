package main

import (
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
	"github.com/BackOfficeAssoc/qzar/pkg/models"
)

var (
	apiSec = authorizer.AuthzDocument{
		CallsiteMap: map[string][]models.ActionID{
			"urn:POST:/samples":       {models.CatalogCreateRuleAction},
			"urn:GET:/samples":        {models.CatalogListRulesAction},
			"urn:GET:/samples/:id":    {models.CatalogViewRuleAction},
			"urn:PUT:/samples/:id":    {models.CatalogEditRuleAction},
			"urn:DELETE:/samples/:id": {models.CatalogDeleteRuleAction},

			"urn:POST:/contexts/:context_id/datamodel":                             {models.CatalogCreateCatalogAction},
			"urn:GET:/contexts/:context_id/datamodel/navigation":                   {models.CatalogViewCatalogAction},
			"urn:DELETE:/contexts/:context_id/datamodel":                           {models.CatalogDeleteCatalogAction},
			"urn:GET:/contexts/:context_id/datamodel/entities":                     {models.CatalogViewCatalogAction},
			"urn:GET:/contexts/:context_id/datamodel/entities/:parent_id/elements": {models.CatalogViewCatalogAction},
		},
	}
)
