type: object
properties:
  tenant_id: 
    type: string
    description: Unique identifier for the tenant
    example: "tnt_123"
  import_id:
    type: string
    description: Unique ID of the created import
  context_id: 
    type: string
    description: Unique identifier for the context
    example: "dst_abc"
  status:
    type: string
    description: Initial status of the import
    enum: [pending, receiving, processing, completed, failed]
    example: pending
  created_at:
    type: string
    format: date-time
    description: Creation timestamp
    example: "2025-05-28T14:25:00Z"
  total_batches:
    type: integer
    example: 10
  batches: 
    type: array
    description: List of batches in the import
    items:  
      $ref: "./ImportDataModelBatch.yaml"
   
