type: object
properties:
  entities:
    type: array
    items:
      $ref: "./Entity.yaml"
    description: |
      The list of entities found in the data model.
  entity_elements:
    type: array
    items:
      $ref: "./EntityElement.yaml"
    description: |
      The list of entity elements found in the data model.
  relationships:
    type: array
    items:
      $ref: "./Relationship.yaml"
    description: |
      The list of relationships found in the data model.
  actions:
    type: array
    description: Actions for components (insert, update, delete)
    items:
      $ref: "./ImportDataModelBatchAction.yaml"
required: [actions]
