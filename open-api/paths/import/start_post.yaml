tags:
  - Import DataModel
summary: Create Data Model Import
operationId: createImport
description: |
  The API enables batch import of data models, accommodating scenarios where the MetaScanner scans technical data models with thousands of tables and columns.


  **How It Works**

  1. Logical Transaction Identifier:  
    The entire import process must be handled as a single transaction to ensure the local data model is correctly updated with the scanned data model.
    A `scan_id` is generated as part of a SystemScan entry in the Knowledge Graph.
    This `scan_id` ensures that all batches are imported as part of the same transaction.
  2. Batch Import:  
    Use the import API with the `scan_id` to import all batches of the data model.
  3. Mark Import as Completed:  
    Once all batches are imported, invoke the API to mark the transaction as complete.

  **Key Points for Consumers**

  - Idempotent: The import process is idempotent, ensuring that re-importing the same batch does not create duplicate data.
  - `scan_id`: The `scan_id` is critical for maintaining transactional integrity. It must be provided consistently for all batch imports.
  - Single Import: Only one batch import is allowed at a time for a specific data model context.
parameters:
  - name: context_id
    description: |
      The identifier of the context for which the data model needs to be imported.
      The context can be a Datastore or a Subject Area. This is a mandatory parameter.
    in: path
    schema:
      type: string
requestBody:
  content:
    application/json:
      schema:
        $ref: "../../schemas/ImportDataModelRequest.yaml"
responses:
  "200":
    description: |
      Created Data Model Import.
    content:
      application/json:
        schema:
          $ref: "../../schemas/ImportDataModelStatus.yaml"
  "400":
    description: Bad Request.
    content:
      application/json:
        schema:
          $ref: "../../schemas/CatalogError.yaml"
          examples:
            example-1:
              summary: Example of a Bad Request
              value:
                request_id: "4567-987653-af456"
                errors:
                  - code: "UNSUPPORTED_META_VERSION"
                    message: "The provided meta-version in the request is not valid. Use 'relational-20250204'."
  "401":
    $ref: "../../schemas/UnAuthorizedError.yaml"
  "403":
    $ref: "../../schemas/ForbiddenError.yaml"
