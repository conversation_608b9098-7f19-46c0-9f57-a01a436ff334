tags:
  - Import DataModel
summary: Commit Data Model Import
operationId: commitImport
description: Commit an inport
parameters:
  - name: context_id
    description: |
      The identifier of the context for which the data model needs to be imported.
      The context can be a Datastore or a Subject Area. This is a mandatory parameter.
    in: path
    schema:
      type: string
  - name: import_id
    description: |
      The unique identifier for the import
    in: path
    schema:
      type: string
responses:
  "200":
    description: |
      Accepted Batch for processing.
    content:
      application/json:
        schema:
          $ref: "../../schemas/ImportDataModelStatus.yaml"
  "400":
    description: Bad Request.
    content:
      application/json:
        schema:
          $ref: "../../schemas/CatalogError.yaml"
          examples:
            example-1:
              summary: Example of a Bad Request
              value:
                request_id: "4567-987653-af456"
                errors:
                  - code: "UNSUPPORTED_META_VERSION"
                    message: "The provided meta-version in the request is not valid. Use 'relational-20250204'."
  "401":
    $ref: "../../schemas/UnAuthorizedError.yaml"
  "403":
    $ref: "../../schemas/ForbiddenError.yaml"
