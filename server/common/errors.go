package common

import (
	"fmt"
	"reflect"
	"strings"
	"unsafe"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/probe"
)

// ValidationErrorBuilder helps builds validation error with the right format
type ValidationErrorBuilder struct {
	errors []dto.ErrorDetail
}

// NewValidationErrorBuilder creates a new validation error builder
func NewValidationErrorBuilder() *ValidationErrorBuilder {
	return &ValidationErrorBuilder{
		errors: make([]dto.ErrorDetail, 0),
	}
}

// AddFieldError adds a field-specific validation error
func (b *ValidationErrorBuilder) AddFieldError(field, message string) *ValidationErrorBuilder {
	b.errors = append(b.errors, dto.ErrorDetail{
		Field:   field,
		Message: message,
	})
	return b
}

// AddGeneralError adds a general validation error without a specific field
func (b *ValidationErrorBuilder) AddGeneralError(message string) *ValidationErrorBuilder {
	b.errors = append(b.errors, dto.ErrorDetail{
		Message: message,
	})
	return b
}

// AddBindingErrors processes Gin binding errors and transforms them into the right format
func (b *ValidationErrorBuilder) AddBindingErrors(err error) *ValidationErrorBuilder {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, fieldError := range validationErrors {
			field := fieldError.Field()
			message := getValidationErrorMessage(fieldError)
			b.AddFieldError(field, message)
		}
	} else {
		// For other binding errors
		b.AddGeneralError(fmt.Sprintf("Invalid request format: %s", err.Error()))
	}
	return b
}

// AddSchemaValidationError processes schema validation errors
func (b *ValidationErrorBuilder) AddSchemaValidationError(err error) *ValidationErrorBuilder {
	// Parse schema validation error and extract field-specific information
	errorMsg := err.Error()

	// Try to extract field information from schema validation errors
	lines := strings.Split(errorMsg, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Remove leading "- " if present
		if strings.HasPrefix(line, "- ") {
			line = line[2:]
		}

		// Try to split on ": " to separate field from message
		if parts := strings.SplitN(line, ": ", 2); len(parts) == 2 {
			field := strings.TrimSpace(parts[0])
			message := strings.TrimSpace(parts[1])
			b.AddFieldError(field, message)
		} else {
			// If we can't parse field information, add as general error
			b.AddGeneralError(line)
		}
	}

	return b
}

// Build creates the final validation error
func (b *ValidationErrorBuilder) Build(c *gin.Context) *models.CatalogError {
	result := CreateValidationErrorWithProbeCorrelation(c, b.errors)
	model := result.ToModel()
	return &model
}

// HasErrors returns true if there are any validation errors
func (b *ValidationErrorBuilder) HasErrors() bool {
	return len(b.errors) > 0
}

// CreateValidationErrorWithProbeCorrelation creates a validation error using the probe's correlation ID
func CreateValidationErrorWithProbeCorrelation(c *gin.Context, details []dto.ErrorDetail) dto.ErrorCreationResult {
	// Get the correlation ID from the probe context
	correlationID := getProbeCorrelationID(c)

	// Fallback for test environments where probe middleware is not running
	if correlationID == "" {
		correlationID = generateTraceIDFallback(c)
	}

	// Create the error using the DTO function
	input := dto.ErrorCreationInput{
		TraceID:      correlationID,
		ErrorDetails: details,
	}

	return dto.CreateValidationError(input)
}

// getProbeCorrelationID extracts the correlation ID from the probe context
func getProbeCorrelationID(c *gin.Context) string {
	if prb := probe.Load(c); prb != nil {
		v := reflect.ValueOf(prb).Elem() // prb is always a pointer, so get the element
		correlationKeyField := v.FieldByName("correlationKey")
		if correlationKeyField.IsValid() && correlationKeyField.CanAddr() {
			ptr := correlationKeyField.UnsafeAddr()
			correlationKeyField = reflect.NewAt(correlationKeyField.Type(), unsafe.Pointer(ptr)).Elem()
			return correlationKeyField.String()
		}
	}
	return ""
}

// generateTraceIDFallback generates a trace ID for test environments where probe middleware is not available
func generateTraceIDFallback(c *gin.Context) string {
	// Try to get trace ID from various possible headers
	traceID := c.GetHeader("X-Trace-ID")
	if traceID == "" {
		traceID = c.GetHeader("X-Request-ID")
	}
	if traceID == "" {
		traceID = c.GetHeader("X-Correlation-ID")
	}

	// If no trace ID found, generate a new UUID
	if traceID == "" {
		traceID = uuid.New().String()
	}

	return traceID
}

// RespondWithError sends a standardized error response
func RespondWithError(c *gin.Context, err *models.CatalogError) {
	c.JSON(err.Status, err)
	c.Abort()
}

// getValidationErrorMessage converts validator field errors to human-readable messages
func getValidationErrorMessage(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", fe.Field())
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", fe.Field(), fe.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", fe.Field(), fe.Param())
	case "email":
		return fmt.Sprintf("%s must be a valid email address", fe.Field())
	case "url":
		return fmt.Sprintf("%s must be a valid URL", fe.Field())
	case "oneof":
		return fmt.Sprintf("%s must be one of: %s", fe.Field(), fe.Param())
	default:
		return fmt.Sprintf("%s is invalid", fe.Field())
	}
}

// HandleBindingError is a utility function to handle Gin binding errors with standardized error response
func HandleBindingError(c *gin.Context, err error) {
	// Log the error for debugging
	probe.Load(c).WithError(err).Emit(probes.ValidationError)

	// Build standardized validation error
	builder := NewValidationErrorBuilder()
	builder.AddBindingErrors(err)

	catalogError := builder.Build(c)
	RespondWithError(c, catalogError)
}

// HandleSchemaValidationError is a utility function to handle schema validation errors
func HandleSchemaValidationError(c *gin.Context, err error) {
	// Log the error for debugging
	probe.Load(c).WithError(err).Emit(probes.ValidationError)

	// Build standardized validation error
	builder := NewValidationErrorBuilder()
	builder.AddSchemaValidationError(err)

	catalogError := builder.Build(c)
	RespondWithError(c, catalogError)
}

// HandleRequiredFieldError is a utility function for required field validation errors
func HandleRequiredFieldError(c *gin.Context, fieldName string) {
	builder := NewValidationErrorBuilder()
	builder.AddFieldError(fieldName, fmt.Sprintf("%s is required", fieldName))

	catalogError := builder.Build(c)
	RespondWithError(c, catalogError)
}

// HandleConflictError is a utility function for conflict errors
func HandleConflictError(c *gin.Context, resource string, detail string) {
	catalogError := CreateConflictErrorWithProbeCorrelation(c, resource, detail)
	RespondWithError(c, catalogError)
}

// CreateConflictErrorWithProbeCorrelation creates a conflict error using the probe's correlation ID
func CreateConflictErrorWithProbeCorrelation(c *gin.Context, resource string, detail string) *models.CatalogError {
	// Get the correlation ID from the probe context
	correlationID := getProbeCorrelationID(c)

	// Fallback for test environments where probe middleware is not running
	if correlationID == "" {
		correlationID = generateTraceIDFallback(c)
	}

	// Create the error using the DTO function
	input := dto.ErrorCreationInput{
		TraceID:  correlationID,
		Resource: resource,
		Detail:   detail,
	}

	result := dto.CreateConflictError(input)
	model := result.ToModel()
	return &model
}

// HandleInternalServerError is a utility function for internal server errors
func HandleInternalServerError(c *gin.Context, detail string) {
	catalogError := CreateInternalServerErrorWithProbeCorrelation(c, detail)
	RespondWithError(c, catalogError)
}

// HandleUnauthorizedError is a utility function for unauthorized errors
func HandleUnauthorizedError(c *gin.Context, detail string) {
	catalogError := CreateUnauthorizedErrorWithProbeCorrelation(c, detail)
	RespondWithError(c, catalogError)
}

// CreateInternalServerErrorWithProbeCorrelation creates an internal server error using the probe's correlation ID
func CreateInternalServerErrorWithProbeCorrelation(c *gin.Context, detail string) *models.CatalogError {
	// Get the correlation ID from the probe context
	correlationID := getProbeCorrelationID(c)

	// Fallback for test environments where probe middleware is not running
	if correlationID == "" {
		correlationID = generateTraceIDFallback(c)
	}

	// Create the error using the DTO function
	input := dto.ErrorCreationInput{
		TraceID: correlationID,
		Detail:  detail,
	}

	result := dto.CreateInternalServerError(input)
	model := result.ToModel()
	return &model
}

// CreateUnauthorizedErrorWithProbeCorrelation creates an unauthorized error using the probe's correlation ID
func CreateUnauthorizedErrorWithProbeCorrelation(c *gin.Context, detail string) *models.CatalogError {
	// Get the correlation ID from the probe context
	correlationID := getProbeCorrelationID(c)

	// Fallback for test environments where probe middleware is not running
	if correlationID == "" {
		correlationID = generateTraceIDFallback(c)
	}

	// Create the error using the DTO function
	input := dto.ErrorCreationInput{
		TraceID: correlationID,
		Detail:  detail,
	}

	result := dto.CreateUnauthorizedError(input)
	model := result.ToModel()
	return &model
}
