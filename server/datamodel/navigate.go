package datamodel

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/auth"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
	"github.com/gin-gonic/gin"
)

// NavigateRepo defines the repository interface for navigation
type NavigateRepo interface {
	NavigateDataModel(
		ctx context.Context,
		tenantID string,
		query models.NavigateDataModelQuery,
	) (dto.Entities, dto.EntityElements, dto.Relationships, string, error)
}

func NavigateDataModel(repository repo.DataModelStore) []gin.HandlerFunc {
	h := navigateHandler{
		repo: repository,
	}
	return h.Chain()
}

type navigateHandler struct {
	repo repo.DataModelStore
}

// navigateRequest holds all parameters for a data model navigation query.
type navigateRequest struct {
	ContextID       string
	ParentID        string
	Types           []string
	Filter          string
	First           int
	Offset          string
	Sort            string
	NameExact       string
	NameText        string
	DescriptionText string
}

// ToModel converts navigateRequest to models.NavigateDataModelQuery
func (req *navigateRequest) ToModel() models.NavigateDataModelQuery {
	return models.NavigateDataModelQuery{
		ContextID:       req.ContextID,
		ParentID:        req.ParentID,
		Types:           req.Types,
		Filter:          req.Filter,
		First:           req.First,
		Offset:          req.Offset,
		Sort:            req.Sort,
		NameExact:       req.NameExact,
		NameText:        req.NameText,
		DescriptionText: req.DescriptionText,
	}
}

// ToDTO converts navigateRequest to dto.NavigateDataModelQuery
func (req *navigateRequest) ToDTO() dto.NavigateDataModelQuery {
	return dto.NavigateDataModelQuery{
		ContextID:       req.ContextID,
		ParentID:        req.ParentID,
		Types:           req.Types,
		Filter:          req.Filter,
		First:           req.First,
		Offset:          req.Offset,
		Sort:            req.Sort,
		NameExact:       req.NameExact,
		NameText:        req.NameText,
		DescriptionText: req.DescriptionText,
	}
}

// Validate
func (req *navigateRequest) Validate() error {
	// all navigation need at least a context_id
	if req.ContextID == "" {
		return fmt.Errorf("context_id is required")
	}
	return nil
}

// ValidateForFind validates the navigateRequest specifically to find entities endpoint
func (req *navigateRequest) ValidateForFind() error {
	// First run basic validation
	if err := req.Validate(); err != nil {
		return err
	}

	// For find endpoint, we don't allow parent_id (entities only, not entity elements)
	if req.ParentID != "" {
		return fmt.Errorf("parent_id is not supported for find entities endpoint")
	}

	return nil
}

// ValidateForFindElements validates the navigateRequest specifically to find entity elements endpoint
func (req *navigateRequest) ValidateForFindElements() error {
	// First run basic validation
	if err := req.Validate(); err != nil {
		return err
	}

	// For find elements endpoint, parent_id is required
	if req.ParentID == "" {
		return fmt.Errorf("parent_id is required for find entity elements endpoint")
	}

	return nil
}

// ValidateForNavigate validates the navigateRequest specifically for navigate endpoint
func (req *navigateRequest) ValidateForNavigate() error {
	// For navigate endpoint, context_id is always required
	if req.ContextID == "" {
		return fmt.Errorf("context_id is required")
	}

	return nil
}

// parseFirstParameter parses the "first" query parameter with a default value
func parseFirstParameter(c *gin.Context, defaultValue int) int {
	firstStr := c.DefaultQuery("first", strconv.Itoa(defaultValue))
	first, err := strconv.Atoi(firstStr)
	if err != nil {
		first = defaultValue
	}
	return first
}

// getQueryParam gets a query parameter using case-insensitive lookup
func getQueryParam(c *gin.Context, expectedParam string) string {
	// First let's try to get the exact expected parameter name
	if value := c.Query(expectedParam); value != "" {
		return value
	}
	// If not found, search without worrying about case-sensitivity
	expectedLower := strings.ToLower(expectedParam)
	for param, values := range c.Request.URL.Query() {
		//Using just strings.ToLower() as requested by Rob
		if strings.ToLower(param) == expectedLower && len(values) > 0 {
			return values[0]
		}
	}

	return ""
}

func (h *navigateHandler) Validate(c *gin.Context) {
	datamodelContextID := c.Param("context_id")
	parentID := c.Query("parent_id")

	// Use QueryArray so I can catch multiple types in the request header
	types := c.QueryArray("type")
	if len(types) == 0 {
		types = []string{}
	}

	req := &navigateRequest{
		ContextID:       datamodelContextID,
		ParentID:        parentID,
		Types:           types,
		Filter:          c.Query("filter"),
		Offset:          c.Query("offset"),
		Sort:            c.Query("sort"),
		NameExact:       getQueryParam(c, "name_exact"),
		NameText:        getQueryParam(c, "name_text"),
		DescriptionText: getQueryParam(c, "description_text"),
	}

	// Pagination parameters
	// the clamping logic is at the repo layer but I just set a default value here
	req.First = parseFirstParameter(c, repo.DefaultPageSize)

	if err := req.ValidateForNavigate(); err != nil {
		httperrors.BadRequest(c, "%s", err.Error())
		c.Abort()
		return
	}
	req.First = parseFirstParameter(c, repo.DefaultPageSize)

	if err := req.ValidateForNavigate(); err != nil {
		httperrors.BadRequest(c, "%s", err.Error())
		c.Abort()
		return
	}

	c.Set("request", req)
}

func (h *navigateHandler) Authorize(c *gin.Context) {
	// Get tenant ID from query parameters when provided, could be useful for global users
	tenantIDFromQuery := c.Query("tenant_id")

	// Use the shared function to get tenant scope
	tenantID, ok := auth.GetTenantScope(c, &tenantIDFromQuery)
	if !ok {
		c.Abort()
		return
	}

	authZ, err := authorizer.Output(c)
	if err != nil {
		httperrors.StatusText(c, http.StatusUnauthorized)
		c.Abort()
		return
	}

	// Set tenant ID and user ID in context
	c.Set("tenantID", *tenantID)
	c.Set("tenantID", *tenantID)
	c.Set("user_id", authZ.UserID)
}

func (h *navigateHandler) Handle(c *gin.Context) {
	req := c.MustGet("request").(*navigateRequest)
	tenantID := c.MustGet("tenantID").(string)

	// Call the repository using the new ToDTO method
	result, repoErr := h.repo.NavigateDataModel(
		c,
		tenantID,
		req.ToDTO(),
	)

	if repoErr != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": repoErr.Error(),
		})
		return
	}

	resp := result.ToNavigateResponse()
	c.JSON(http.StatusOK, resp)
}

func (h *navigateHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}
