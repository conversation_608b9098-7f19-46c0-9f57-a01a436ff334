package datamodel_test

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/server/datamodel"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestCreateDataModel_StandardizedValidationError() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusBadRequest
	datamodelContextID := "dst_111"

	// Mock repository
	repo := &fixtures.RepoMock{}

	// Create request with missing required fields to trigger validation errors
	datamodelRequest := models.DataModel{
		// Missing MetaSchemaVersion - should trigger validation error
		TenantID: testTenantID,
		// Missing Entities and Relationships - should trigger validation error
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Create(repo)...)

	w, err := s.NewRequest("POST").
		WithPath("context_id", datamodelContextID).
		WithJSON(&datamodelRequest).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response status
	s.Equal(expectedStatus, w.Code)

	// Parse the standardized error response
	var errorResponse models.CatalogError
	err = json.Unmarshal(w.Body.Bytes(), &errorResponse)
	s.Require().NoError(err, "failed to unmarshal error response")

	// Verify standardized error structure
	s.NotEmpty(errorResponse.TraceID, "trace_id should be present")
	s.Equal(models.ErrorTypeValidation, errorResponse.Type)
	s.Equal(http.StatusBadRequest, errorResponse.Status)
	s.Equal(models.ErrorTitleValidation, errorResponse.Title)
	s.NotEmpty(errorResponse.Detail, "detail should be present")
	s.NotEmpty(errorResponse.Message, "message should be present for backward compatibility")

	// Debug: Print the response to understand what's happening
	s.T().Logf("Actual error response: %+v", errorResponse)
	for i, err := range errorResponse.Errors {
		s.T().Logf("Error %d: Field='%s', Message='%s'", i, err.Field, err.Message)
	}

	// Verify specific validation errors - should have at least some validation errors
	s.Require().GreaterOrEqual(len(errorResponse.Errors), 1, "should have at least 1 validation error")

	// Check for validation errors (could be from binding or custom validation)
	foundValidationError := false
	for _, errDetail := range errorResponse.Errors {
		if errDetail.Field == "MetaSchemaVersion" || errDetail.Field == "meta_schema_version" {
			foundValidationError = true
			s.Contains(errDetail.Message, "required", "should mention that field is required")
		}
		if errDetail.Field == "Entities" || errDetail.Message == "either entities or relationships must be provided and not be empty" {
			foundValidationError = true
		}
	}
	s.True(foundValidationError, "should have at least one validation error for required fields")
}

func (s *ChainTestSuite) TestCreateDataModel_StandardizedBindingError() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusBadRequest
	datamodelContextID := "dst_111"

	// Mock repository
	repo := &fixtures.RepoMock{}

	// Create invalid JSON to trigger binding error
	invalidJSON := `{"invalid": json}`

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Create(repo)...)

	w, err := s.NewRequest("POST").
		WithPath("context_id", datamodelContextID).
		WithBody(strings.NewReader(invalidJSON)).
		WithHeader("Content-Type", "application/json").
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response status
	s.Equal(expectedStatus, w.Code)

	// Parse the standardized error response
	var errorResponse models.CatalogError
	err = json.Unmarshal(w.Body.Bytes(), &errorResponse)
	s.Require().NoError(err, "failed to unmarshal error response")

	// Verify standardized error structure
	s.NotEmpty(errorResponse.TraceID, "trace_id should be present")
	s.Equal(models.ErrorTypeValidation, errorResponse.Type)
	s.Equal(http.StatusBadRequest, errorResponse.Status)
	s.Equal(models.ErrorTitleValidation, errorResponse.Title)
	s.NotEmpty(errorResponse.Detail, "detail should be present")
	s.Contains(errorResponse.Detail, "Invalid request format", "should mention invalid request format")

	// Should have at least one error detail
	s.Require().GreaterOrEqual(len(errorResponse.Errors), 1, "should have at least one error detail")
	s.Contains(errorResponse.Errors[0].Message, "Invalid request format", "error message should mention invalid format")
}

func (s *ChainTestSuite) TestCreateDataModel_StandardizedSchemaValidationError() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusBadRequest
	datamodelContextID := "dst_111"

	// Mock repository
	repo := &fixtures.RepoMock{}

	// Create request with invalid entity type to trigger schema validation error
	datamodelRequest := models.DataModel{
		MetaSchemaVersion: "relational-20250501",
		TenantID:          testTenantID,
		Entities: []models.Entity{
			{
				ModelName: "ns1.production.product",
				Name:      "ProductEntity",
				Type:      "INVALID_TYPE", // This should trigger schema validation error
			},
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Create(repo)...)

	w, err := s.NewRequest("POST").
		WithPath("context_id", datamodelContextID).
		WithJSON(&datamodelRequest).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response status
	s.Equal(expectedStatus, w.Code)

	// Parse the standardized error response
	var errorResponse models.CatalogError
	err = json.Unmarshal(w.Body.Bytes(), &errorResponse)
	s.Require().NoError(err, "failed to unmarshal error response")

	// Verify standardized error structure
	s.NotEmpty(errorResponse.TraceID, "trace_id should be present")
	s.Equal(models.ErrorTypeValidation, errorResponse.Type)
	s.Equal(http.StatusBadRequest, errorResponse.Status)
	s.Equal(models.ErrorTitleValidation, errorResponse.Title)
	s.NotEmpty(errorResponse.Detail, "detail should be present")

	// Should have error details from schema validation
	s.Require().GreaterOrEqual(len(errorResponse.Errors), 1, "should have at least one schema validation error")
}
