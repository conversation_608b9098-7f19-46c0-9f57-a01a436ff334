package datamodel

import (
	"context"
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

type DeleteRepo interface {
	DeleteDataModel(ctx context.Context, tenantID string, contextID string, tx *sql.Tx) (*models.DeleteDataModelResponse, error)
}

func Delete(r DeleteRepo) []gin.HandlerFunc {
	h := deleteHandler{
		repo: r,
	}
	return h.Chain()
}

type deleteHandler struct {
	repo DeleteRepo
}

type deleteRequest struct {
	models.DeleteDataModelRequest
}

func (h *deleteHandler) Validate(c *gin.Context) {
	var req deleteRequest
	if c.Param("context_id") == "" {
		httperrors.BadRequest(c, "context_id is required from parameter")
		return
	}
	c.Set("request", &req)
}

func (h *deleteHandler) Authorize(c *gin.Context) {
	req := c.MustGet("request").(*deleteRequest)
	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	}

	if !authZ.HasGlobalAccess() {
		req.TenantID = authZ.TenantID
	} else if req.TenantID == nil {
		probe.Load(c).WithField("user_id", authZ.UserID).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		return
	}

	c.Set("user_id", authZ.UserID)
}

func (h *deleteHandler) Handle(c *gin.Context) {
	var (
		req = c.MustGet("request").(*deleteRequest)
		prb = probe.Load(c)
		ctx = c.Request.Context()
	)

	ContextID := c.Param("context_id")
	response, err := h.repo.DeleteDataModel(ctx, *req.TenantID, ContextID, nil)
	if err != nil {
		// Use prb and error handling as before, or wrap as needed
		prb.WithError(err).Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error while deleting data model")
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *deleteHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}
