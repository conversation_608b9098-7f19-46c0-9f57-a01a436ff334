package datamodel

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/server/auth"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

func Find(repository repo.DataModelStore) []gin.HandlerFunc {
	h := findHandler{
		repo: repository,
	}
	return h.Chain()
}

type findHandler struct {
	repo repo.DataModelStore
}

// parseNavigateRequestForFind parses query parameters into a navigateRequest structure
func parseNavigateRequestForFind(c *gin.Context) (*navigateRequest, error) {
	contextID := c.Param("context_id")
	parentID := c.Query("parent_id")

	// Use QueryArray so I can catch multiple types in the request header
	types := c.QueryArray("type")
	if len(types) == 0 {
		types = []string{}
	}

	req := &navigateRequest{
		ContextID:       contextID,
		ParentID:        parentID,
		Types:           types,
		Filter:          c.Query("filter"),
		Offset:          c.Query("offset"),
		Sort:            c.Query("sort"),
		NameExact:       getQueryParam(c, "name_exact"),
		NameText:        getQueryParam(c, "name_text"),
		DescriptionText: getQueryParam(c, "description_text"),
	}

	req.First = parseFirstParameter(c, 100) // Default for find endpoints

	return req, nil
}

func (h *findHandler) Validate(c *gin.Context) {
	// Parse query parameters using the same logic as navigate
	req, err := parseNavigateRequestForFind(c)
	if err != nil {
		httperrors.BadRequest(c, "Failed to parse query parameters: %s", err.Error())
		c.Abort()
		return
	}

	if err := req.ValidateForFind(); err != nil {
		httperrors.BadRequest(c, "%s", err.Error())
		c.Abort()
		return
	}

	c.Set("request", req)
}

func (h *findHandler) Authorize(c *gin.Context) {
	// Get tenant ID from query parameters when provided, could be useful for global users
	tenantIDFromQuery := c.Query("tenant_id")

	// Use the shared function to get tenant scope
	tenantID, ok := auth.GetTenantScope(c, &tenantIDFromQuery)
	if !ok {
		c.Abort()
		return
	}

	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		c.Abort()
		return
	}

	// Set tenant ID and user ID in context
	c.Set("tenantID", *tenantID)
	c.Set("user_id", authZ.UserID)
}

func (h *findHandler) Handle(c *gin.Context) {
	req := c.MustGet("request").(*navigateRequest)
	tenantID := c.MustGet("tenantID").(string)

	// Convert to NavigateDataModelQuery DTO using the new ToDTO method
	navQuery := req.ToDTO()

	// Call the repository using the same navigation logic
	result, err := h.repo.NavigateDataModel(
		c,
		tenantID,
		navQuery,
	)

	if err != nil {
		probe.Load(c).WithError(err).Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error while finding entities")
		return
	}

	// Since this is find entities endpoint, we should only have entities, this is me beeing extra careful
	if len(result.EntityElements) > 0 {
		probe.Load(c).Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected entity elements returned for find entities endpoint")
		return
	}

	resp := result.ToNavigateResponse()
	c.JSON(http.StatusOK, resp)
}

func (h *findHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}
