package datamodel

import (
	"context"
	"database/sql"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	metameta "github.com/BackOfficeAssoc/catalog/meta-meta"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/auth"
	"github.com/BackOfficeAssoc/catalog/server/common"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

type CreateRepo interface {
	CreateEntities(ctx context.Context, tx *sql.Tx, entities dto.Entities) error
	CreateEntityElements(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error
	CreateRelationships(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error
	CreateBranch(ctx context.Context, tx *sql.Tx, branch dto.Branch) (string, error)
	CreateVersion(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error)
	GenerateBranchAndVersionID(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string)
}

func Create(r CreateRepo) []gin.HandlerFunc {
	h := createHandler{
		repo: r,
	}
	return h.Chain()
}

type createHandler struct {
	repo CreateRepo
}

// createRequest represents the request body for creating a data model
type createRequest struct {
	models.CreateDataModelRequest
}

func (h *createHandler) Validate(c *gin.Context) {
	var req createRequest
	if err := c.ShouldBindWith(&req, binding.JSON); err != nil {
		common.HandleBindingError(c, err)
		return
	}

	datamodelContextID := c.Param("context_id")

	// Build validation errors using the new error builder
	builder := common.NewValidationErrorBuilder()

	// Validate required fields
	if req.MetaSchemaVersion == "" {
		builder.AddFieldError("meta_schema_version", "meta_schema_version is required")
	}
	if datamodelContextID == "" {
		builder.AddFieldError("context_id", "context_id is required")
	}

	// Allow either entities or relationships to be provided, but at least one must be present
	if len(req.Entities) == 0 && len(req.Relationships) == 0 {
		builder.AddGeneralError("either entities or relationships must be provided and not be empty")
	}

	// If we have validation errors, return them before schema validation
	if builder.HasErrors() {
		catalogError := builder.Build(c)
		common.RespondWithError(c, catalogError)
		return
	}

	// Validate the incoming payload using the schema
	if err := validateAgainstSchema(req.CreateDataModelRequest); err != nil {
		common.HandleSchemaValidationError(c, err)
		return
	}

	c.Set("request", &req)
}

func (h *createHandler) Authorize(c *gin.Context) {
	req := c.MustGet("request").(*createRequest)

	// Convert tenant ID pointer to string for GetTenantScope
	var tenantIDFromRequest *string
	if req.TenantID != nil {
		tenantIDFromRequest = req.TenantID
	}

	// Use the shared function to get tenant scope
	tenantID, ok := auth.GetTenantScope(c, tenantIDFromRequest)
	if !ok {
		c.Abort()
		return
	}

	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		common.HandleUnauthorizedError(c, "Authentication information is expired, missing, or invalid.")
		return
	}

	// Set the validated tenant ID back to the request
	req.TenantID = tenantID

	// Set tenant ID and user ID in context
	c.Set("tenantID", *tenantID)
	c.Set("user_id", authZ.UserID)
}

func (h *createHandler) Handle(c *gin.Context) {
	var (
		datamodelContextID = c.Param("context_id")
		req                = c.MustGet("request").(*createRequest)
		userID             = c.MustGet("user_id").(string)
		prb                = probe.Load(c)
		ctx                = c.Request.Context()
	)
	branchID, versionID := h.repo.GenerateBranchAndVersionID(ctx, *req.TenantID, datamodelContextID, userID, nil)

	// Process entities if the array is not empty
	if len(req.Entities) > 0 {
		var dtoEntities dto.Entities
		for _, entity := range req.Entities {
			var dtoEntity dto.Entity
			dtoEntity.FromModel(entity)
			dtoEntity.TenantID = *req.TenantID
			dtoEntity.MetaVersion = req.MetaSchemaVersion
			dtoEntity.ContextID = datamodelContextID
			dtoEntity.CreatedBy = userID
			dtoEntity.BranchID = branchID
			dtoEntity.VersionID = versionID

			dtoEntity.EntityElements = make([]dto.EntityElement, len(entity.EntityElements))
			for i, element := range entity.EntityElements {
				var dtoElement dto.EntityElement
				dtoElement.FromModel(element)
				dtoElement.TenantID = *req.TenantID
				dtoElement.MetaVersion = req.MetaSchemaVersion
				dtoElement.ContextID = datamodelContextID
				dtoElement.CreatedBy = userID
				dtoElement.BranchID = branchID
				dtoElement.VersionID = versionID

				dtoEntity.EntityElements[i] = dtoElement
			}

			dtoEntities = append(dtoEntities, dtoEntity)
		}

		// Create entities in the repository
		if err := h.repo.CreateEntities(ctx, nil, dtoEntities); err != nil {
			if errors.Is(err, repo.ErrConflict) {
				common.HandleConflictError(c, "entity", "One or more entities already exist in the data model.")
				return
			}

			prb.WithError(err).
				WithField("func", "CreateEntities").
				Emit(probes.RepoError)
			common.HandleInternalServerError(c, "An unexpected error occurred while creating entities in the data model.")
			return
		}

		// Now create entity elements in repository for each entity
		for _, entity := range dtoEntities {
			if len(entity.EntityElements) > 0 {
				if err := h.repo.CreateEntityElements(ctx, nil, entity.ID, entity.EntityElements); err != nil {
					prb.WithError(err).
						WithField("func", "CreateEntityElements").
						WithField("entity_id", entity.ID).
						Emit(probes.RepoError)
					common.HandleInternalServerError(c, "An unexpected error occurred while creating entity elements.")
					return
				}
			}
		}
	}

	// Process relationships if any
	if len(req.Relationships) > 0 {
		var dtoRelationships dto.Relationships

		// Convert model relationships to dto relationships
		for _, relationship := range req.Relationships {
			var dtoRelationship dto.ExtendedRelationship
			dtoRelationship.FromModel(relationship)
			dtoRelationship.TenantID = *req.TenantID
			dtoRelationship.MetaVersion = req.MetaSchemaVersion
			dtoRelationship.ContextID = datamodelContextID
			dtoRelationship.CreatedBy = userID
			dtoRelationship.BranchID = branchID
			dtoRelationship.VersionID = versionID
			dtoRelationships = append(dtoRelationships, dtoRelationship)
		}

		// Create relationships in the repository
		if err := h.repo.CreateRelationships(ctx, nil, dtoRelationships); err != nil {
			prb.WithError(err).
				WithField("func", "CreateRelationships").
				Emit(probes.RepoError)
			common.HandleInternalServerError(c, "An unexpected error occurred while creating relationships in the data model.")
			return
		}
	}

	// Prepare response
	response := models.CreateDataModelResponse{
		MetaSchemaVersion: req.MetaSchemaVersion,
		ContextID:         datamodelContextID,
		NewVersion:        versionID,
	}

	c.JSON(http.StatusOK, response)
}

func (h *createHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}

var validateAgainstSchema = metameta.ValidateAgainstSchema
