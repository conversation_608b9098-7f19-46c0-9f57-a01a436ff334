package datamodel_test

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/datamodel"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestFindEntities_Success() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusOK
	datamodelContextID := "dst_111"

	// Expected response data
	expectedEntities := dto.Entities{
		{ID: "ent_1", Name: "Entity1", Type: "TABLE", ContextID: datamodelContextID, TenantID: testTenantID, MetaVersion: "v1"},
		{ID: "ent_2", Name: "Entity2", Type: "VIEW", ContextID: datamodelContextID, TenantID: testTenantID, MetaVersion: "v1"},
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		NavigateDataModelFunc: func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
			return &dto.NavigateResult{
				Entities:       expectedEntities,
				EntityElements: dto.EntityElements{},
				Relationships:  dto.Relationships{},
				NextToken:      "",
			}, nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Find(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodelContextID).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(w.Body.String())
	}

	// Parse response
	var respBody dto.NavigateResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal response")

	// Verify response content
	s.Equal(2, len(respBody.Entities))
	s.Equal("ent_1", respBody.Entities[0].ID)
	s.Equal("Entity1", respBody.Entities[0].Name)
	s.Equal("TABLE", respBody.Entities[0].Type)
	s.Equal("ent_2", respBody.Entities[1].ID)
	s.Equal("Entity2", respBody.Entities[1].Name)
	s.Equal("VIEW", respBody.Entities[1].Type)

	// Verify the repository calls
	calls := repo.NavigateDataModelCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.NavigateDataModelCalls")
	s.Equal(testTenantID, calls[0].TenantID)
	s.Equal(datamodelContextID, calls[0].Query.ContextID)
}

func (s *ChainTestSuite) TestFindEntities_WithFilters() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusOK
	datamodelContextID := "dst_111"

	// Expected response data
	expectedEntities := dto.Entities{
		{ID: "ent_1", Name: "TableEntity", Type: "TABLE", ContextID: datamodelContextID, TenantID: testTenantID, MetaVersion: "v1"},
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		NavigateDataModelFunc: func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
			return &dto.NavigateResult{
				Entities:       expectedEntities,
				EntityElements: dto.EntityElements{},
				Relationships:  dto.Relationships{},
				NextToken:      "",
			}, nil
		},
	}

	// Invoke the handler with query parameters
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Find(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodelContextID).
		WithQuery("type", "TABLE").
		WithQuery("name_text", "Table").
		WithQuery("first", "10").
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(w.Body.String())
	}

	// Parse response
	var respBody dto.NavigateResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal response")

	// Verify response content
	s.Equal(1, len(respBody.Entities))
	s.Equal("ent_1", respBody.Entities[0].ID)
	s.Equal("TableEntity", respBody.Entities[0].Name)
	s.Equal("TABLE", respBody.Entities[0].Type)

	// Verify the repository calls
	calls := repo.NavigateDataModelCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.NavigateDataModelCalls")
	s.Equal(testTenantID, calls[0].TenantID)
	s.Equal(datamodelContextID, calls[0].Query.ContextID)
	s.Equal([]string{"TABLE"}, calls[0].Query.Types)
	s.Equal("Table", calls[0].Query.NameText)
	s.Equal(10, calls[0].Query.First)
}

func (s *ChainTestSuite) TestFindEntities_InvalidParentID() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusBadRequest
	datamodelContextID := "dst_111"

	// Mock repository
	repo := &fixtures.RepoMock{}

	// Invoke the handler with parent_id (not allowed for find entities)
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Find(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodelContextID).
		WithQuery("parent_id", "some_parent"). // parent_id not allowed for find entities
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	s.Equal(expectedStatus, w.Code)

	// Verify no repository calls were made
	calls := repo.NavigateDataModelCalls()
	s.Equal(0, len(calls), "Should not make any repo calls when validation fails")
}

func (s *ChainTestSuite) TestFindEntities_Unauthorized() {
	// Prepare test data
	expectedStatus := http.StatusUnauthorized
	datamodelContextID := "dst_111"

	// Mock repository
	repo := &fixtures.RepoMock{}

	// Invoke the handler with empty user ID (unauthorized)
	authz := authorizertesting.New("") // Empty user ID
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Find(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodelContextID).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	s.Equal(expectedStatus, w.Code)

	// Verify no repository calls were made
	calls := repo.NavigateDataModelCalls()
	s.Equal(0, len(calls), "Should not make any repo calls when unauthorized")
}
