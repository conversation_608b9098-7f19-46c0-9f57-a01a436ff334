package datamodel_test

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/datamodel"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestNavigateDataModel_Success_FetchAllEntities() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusOK
	datamodel_context_id := "dst_111"

	// Expected response data
	expectedEntities := dto.Entities{
		{ID: "ent_1", Name: "Entity1", Type: "TABLE", ContextID: datamodel_context_id, TenantID: testTenantID, MetaVersion: "v1"},
		{ID: "ent_2", Name: "Entity2", Type: "TABLE", ContextID: datamodel_context_id, TenantID: testTenantID, MetaVersion: "v1"},
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		NavigateDataModelFunc: func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
			return &dto.NavigateResult{
				Entities:       expectedEntities,
				EntityElements: dto.EntityElements{},
				Relationships:  dto.Relationships{},
				NextToken:      "",
			}, nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.NavigateDataModel(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodel_context_id).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(w.Body.String())
	}

	var respBody dto.NavigateResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal response")

	s.Equal(2, len(respBody.Entities))
	s.Equal(expectedEntities[0].ID, respBody.Entities[0].ID)
	s.Equal(expectedEntities[0].Name, respBody.Entities[0].Name)

	// Verify the repository calls
	calls := repo.NavigateDataModelCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.NavigateDataModelCalls")
}

func (s *ChainTestSuite) TestNavigateDataModel_FetchEntitiesByType() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusOK
	datamodel_context_id := "dst_111"

	// Expected response data
	expectedEntities := dto.Entities{
		{ID: "ent_1", Name: "Table1", Type: "table", ContextID: datamodel_context_id, TenantID: testTenantID, MetaVersion: "v1"},
		{ID: "ent_2", Name: "Table2", Type: "table", ContextID: datamodel_context_id, TenantID: testTenantID, MetaVersion: "v1"},
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		NavigateDataModelFunc: func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
			return &dto.NavigateResult{
				Entities:       expectedEntities,
				EntityElements: dto.EntityElements{},
				Relationships:  dto.Relationships{},
				NextToken:      "",
			}, nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.NavigateDataModel(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodel_context_id).
		WithQuery("type", "table").
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(w.Body.String())
	}

	var respBody dto.NavigateResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal response")

	s.Equal(2, len(respBody.Entities))
	s.Equal(expectedEntities[0].ID, respBody.Entities[0].ID)
	s.Equal(expectedEntities[1].ID, respBody.Entities[1].ID)

	// Verify the repository calls
	calls := repo.NavigateDataModelCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.NavigateDataModelCalls")
}

func (s *ChainTestSuite) TestNavigateDataModel_FetchEntityElements() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusOK
	datamodel_context_id := "dst_111"
	parent_id := "ent_123"

	// Expected response data
	expectedElements := dto.EntityElements{
		{ID: "ele_1", Name: "Column1", Type: "column", ParentID: parent_id, TenantID: testTenantID, MetaVersion: "v1"},
		{ID: "ele_2", Name: "Column2", Type: "column", ParentID: parent_id, TenantID: testTenantID, MetaVersion: "v1"},
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		NavigateDataModelFunc: func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
			return &dto.NavigateResult{
				Entities:       dto.Entities{},
				EntityElements: expectedElements,
				Relationships:  dto.Relationships{},
				NextToken:      "",
			}, nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.NavigateDataModel(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodel_context_id).
		WithQuery("parent_id", parent_id).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(w.Body.String())
	}

	var respBody dto.NavigateResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal response")

	s.Equal(2, len(respBody.EntityElements))
	s.Equal(expectedElements[0].ID, respBody.EntityElements[0].ID)
	s.Equal(expectedElements[1].ID, respBody.EntityElements[1].ID)

	// Verify the repository calls
	calls := repo.NavigateDataModelCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.NavigateDataModelCalls")
}

func (s *ChainTestSuite) TestNavigateDataModel_RepoError() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusInternalServerError
	datamodel_context_id := "dst_111"

	// Mock repository that returns an error
	repo := &fixtures.RepoMock{
		NavigateDataModelFunc: func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
			return nil, context.DeadlineExceeded
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.NavigateDataModel(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodel_context_id).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	s.Equal(expectedStatus, w.Code)

	// Verify the repository calls
	calls := repo.NavigateDataModelCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.NavigateDataModelCalls")
}

func (s *ChainTestSuite) TestFindElements_Success() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusOK
	datamodel_context_id := "dst_111"
	parent_id := "ent_H88P7ARPprug9ddWqgivQdPXiqr"

	// Expected response data
	expectedElements := dto.EntityElements{
		{ID: "ele_1", Name: "Column1", Type: "COLUMN", ParentID: parent_id, TenantID: testTenantID, MetaVersion: "v1"},
		{ID: "ele_2", Name: "Column2", Type: "COLUMN", ParentID: parent_id, TenantID: testTenantID, MetaVersion: "v1"},
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		NavigateDataModelFunc: func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
			return &dto.NavigateResult{
				Entities:       dto.Entities{},
				EntityElements: expectedElements,
				Relationships:  dto.Relationships{},
				NextToken:      "",
			}, nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.FindElements(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodel_context_id).
		WithPath("parent_id", parent_id).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(w.Body.String())
	}

	var respBody dto.NavigateResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal response")

	s.Equal(2, len(respBody.EntityElements))
	s.Equal(expectedElements[0].ID, respBody.EntityElements[0].ID)
	s.Equal(expectedElements[0].Name, respBody.EntityElements[0].Name)
	s.Equal(expectedElements[1].ID, respBody.EntityElements[1].ID)
	s.Equal(expectedElements[1].Name, respBody.EntityElements[1].Name)

	// Verify the repository calls
	calls := repo.NavigateDataModelCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.NavigateDataModelCalls")
}
