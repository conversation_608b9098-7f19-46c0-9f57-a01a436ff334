package datamodel_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/server/datamodel"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestDeleteDataModel_Success() {
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	contextID := "dst_111"
	expectedStatus := http.StatusOK

	// Mock repository
	repo := &fixtures.RepoMock{
		DeleteDataModelFunc: func(ctx context.Context, tenantID string, contextID string, tx *sql.Tx) (*models.DeleteDataModelResponse, error) {
			return &models.DeleteDataModelResponse{
				ContextID:            contextID,
				EntitiesDeleted:      4,
				ElementsDeleted:      3,
				RelationshipsDeleted: 2,
				VersionsDeleted:      1,
			}, nil
		},
	}

	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Delete(repo)...)

	w, err := s.NewRequest("DELETE").
		WithPath("context_id", contextID).
		Send(handlers...)
	s.Require().NoError(err, "failed to send olives request")

	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(string(w.Body.Bytes()))
	}
	if expectedStatus != http.StatusOK {
		return
	}

	var respBody models.DeleteDataModelResponse
	err = json.Unmarshal(w.Body.Bytes(), &respBody)
	s.Require().NoError(err, "failed to unmarshal olives request")

	s.Equal(contextID, respBody.ContextID)
	s.Equal(int64(4), respBody.EntitiesDeleted)
	s.Equal(int64(3), respBody.ElementsDeleted)
	s.Equal(int64(2), respBody.RelationshipsDeleted)
	s.Equal(int64(1), respBody.VersionsDeleted)
}

func (s *ChainTestSuite) TestDeleteDataModel_MissingContextID() {
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusBadRequest

	repo := &fixtures.RepoMock{}
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Delete(repo)...)

	w, err := s.NewRequest("DELETE").
		Send(handlers...)
	s.Require().NoError(err, "failed to send olives request")

	s.Equal(expectedStatus, w.Code)
}

func (s *ChainTestSuite) TestDeleteDataModel_RepoError() {
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	contextID := "ctx_123"
	expectedStatus := http.StatusInternalServerError

	repo := &fixtures.RepoMock{
		DeleteDataModelFunc: func(ctx context.Context, tenantID string, contextID string, tx *sql.Tx) (*models.DeleteDataModelResponse, error) {
			return nil, errors.New("repo error")
		},
	}
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Delete(repo)...)

	w, err := s.NewRequest("DELETE").
		WithPath("context_id", contextID).
		Send(handlers...)
	s.Require().NoError(err, "failed to send olives request")

	s.Equal(expectedStatus, w.Code)
}
