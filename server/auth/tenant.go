package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

// GetTenantScope extracts and validates tenant scope from authorization context
func GetTenantScope(c *gin.Context, reqTenantID *string) (*string, bool) {
	authZ, err := authorizer.Output(c)
	if err != nil {
		httperrors.StatusText(c, http.StatusUnauthorized)
		return nil, false
	}

	// Global scoped
	if authZ.HasGlobalAccess() {
		if IsUndefined(reqTenantID) {
			httperrors.BadRequest(c, "Global users must specify 'tenant_id' query parameter for this endpoint.")
			return nil, false
		}
		return reqTenantID, true
	}

	// Tenant scoped - for non-global users, use their tenant ID
	if authZ.TenantID == nil {
		httperrors.StatusText(c, http.StatusUnauthorized)
		return nil, false
	}

	return authZ.TenantID, true
}

// IsUndefined checks if a string pointer is nil or points to an empty string
func IsUndefined(s *string) bool {
	return s == nil || *s == ""
}
