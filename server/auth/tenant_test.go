package auth

import (
	"testing"
)

func TestIsUndefined(t *testing.T) {
	tests := []struct {
		name     string
		input    *string
		expected bool
	}{
		{
			name:     "nil pointer",
			input:    nil,
			expected: true,
		},
		{
			name:     "empty string",
			input:    stringPtr(""),
			expected: true,
		},
		{
			name:     "non-empty string",
			input:    stringPtr("test"),
			expected: false,
		},
		{
			name:     "whitespace string",
			input:    stringPtr(" "),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsUndefined(tt.input)
			if result != tt.expected {
				t.Errorf("IsUndefined() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
