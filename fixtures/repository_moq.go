// Code generated by moq; DO NOT EDIT.
// github.com/matryer/moq

package fixtures

import (
	"context"
	"database/sql"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	dto "github.com/BackOfficeAssoc/catalog/repo/dto"
	"sync"
)

// Ensure, that RepoMock does implement repo.Repo.
// If this is not the case, regenerate this file with moq.
var _ repo.Repo = &RepoMock{}

// RepoMock is a mock implementation of repo.Repo.
//
//	func TestSomethingThatUsesRepo(t *testing.T) {
//
//		// make and configure a mocked repo.Repo
//		mockedRepo := &RepoMock{
//			CreateBranchFunc: func(ctx context.Context, tx *sql.Tx, branch dto.Branch) (string, error) {
//				panic("mock out the CreateBranch method")
//			},
//			CreateEntitiesFunc: func(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
//				panic("mock out the CreateEntities method")
//			},
//			CreateEntityElementsFunc: func(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error {
//				panic("mock out the CreateEntityElements method")
//			},
//			CreateRelationshipsFunc: func(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error {
//				panic("mock out the CreateRelationships method")
//			},
//			CreateVersionFunc: func(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error) {
//				panic("mock out the CreateVersion method")
//			},
//			DeleteDataModelFunc: func(ctx context.Context, tenantID string, contextID string, tx *sql.Tx) (*models.DeleteDataModelResponse, error) {
//				panic("mock out the DeleteDataModel method")
//			},
//			FindEntitiesFunc: func(ctx context.Context, query dto.FindEntitiesQuery) (dto.Entities, error) {
//				panic("mock out the FindEntities method")
//			},
//			FindEntityElementsFunc: func(ctx context.Context, query dto.FindEntityElementsQuery) (dto.EntityElements, error) {
//				panic("mock out the FindEntityElements method")
//			},
//			FindRelationshipsFunc: func(ctx context.Context, query dto.FindRelationshipQuery) (dto.Relationships, error) {
//				panic("mock out the FindRelationships method")
//			},
//			GenerateBranchAndVersionIDFunc: func(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string) {
//				panic("mock out the GenerateBranchAndVersionID method")
//			},
//			NavigateDataModelFunc: func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
//				panic("mock out the NavigateDataModel method")
//			},
//		}
//
//		// use mockedRepo in code that requires repo.Repo
//		// and then make assertions.
//
//	}
type RepoMock struct {
	// CreateBranchFunc mocks the CreateBranch method.
	CreateBranchFunc func(ctx context.Context, tx *sql.Tx, branch dto.Branch) (string, error)

	// CreateEntitiesFunc mocks the CreateEntities method.
	CreateEntitiesFunc func(ctx context.Context, tx *sql.Tx, entities dto.Entities) error

	// CreateEntityElementsFunc mocks the CreateEntityElements method.
	CreateEntityElementsFunc func(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error

	// CreateRelationshipsFunc mocks the CreateRelationships method.
	CreateRelationshipsFunc func(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error

	// CreateVersionFunc mocks the CreateVersion method.
	CreateVersionFunc func(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error)

	// DeleteDataModelFunc mocks the DeleteDataModel method.
	DeleteDataModelFunc func(ctx context.Context, tenantID string, contextID string, tx *sql.Tx) (*models.DeleteDataModelResponse, error)

	// FindEntitiesFunc mocks the FindEntities method.
	FindEntitiesFunc func(ctx context.Context, query dto.FindEntitiesQuery) (dto.Entities, error)

	// FindEntityElementsFunc mocks the FindEntityElements method.
	FindEntityElementsFunc func(ctx context.Context, query dto.FindEntityElementsQuery) (dto.EntityElements, error)

	// FindRelationshipsFunc mocks the FindRelationships method.
	FindRelationshipsFunc func(ctx context.Context, query dto.FindRelationshipQuery) (dto.Relationships, error)

	// GenerateBranchAndVersionIDFunc mocks the GenerateBranchAndVersionID method.
	GenerateBranchAndVersionIDFunc func(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string)

	// NavigateDataModelFunc mocks the NavigateDataModel method.
	NavigateDataModelFunc func(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error)

	// calls tracks calls to the methods.
	calls struct {
		// CreateBranch holds details about calls to the CreateBranch method.
		CreateBranch []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Branch is the branch argument value.
			Branch dto.Branch
		}
		// CreateEntities holds details about calls to the CreateEntities method.
		CreateEntities []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Entities is the entities argument value.
			Entities dto.Entities
		}
		// CreateEntityElements holds details about calls to the CreateEntityElements method.
		CreateEntityElements []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// ParentID is the parentID argument value.
			ParentID string
			// EntityElements is the entityElements argument value.
			EntityElements dto.EntityElements
		}
		// CreateRelationships holds details about calls to the CreateRelationships method.
		CreateRelationships []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Relationships is the relationships argument value.
			Relationships dto.Relationships
		}
		// CreateVersion holds details about calls to the CreateVersion method.
		CreateVersion []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tx is the tx argument value.
			Tx *sql.Tx
			// Version is the version argument value.
			Version dto.Version
		}
		// DeleteDataModel holds details about calls to the DeleteDataModel method.
		DeleteDataModel []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// TenantID is the tenantID argument value.
			TenantID string
			// ContextID is the contextID argument value.
			ContextID string
			// Tx is the tx argument value.
			Tx *sql.Tx
		}
		// FindEntities holds details about calls to the FindEntities method.
		FindEntities []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Query is the query argument value.
			Query dto.FindEntitiesQuery
		}
		// FindEntityElements holds details about calls to the FindEntityElements method.
		FindEntityElements []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Query is the query argument value.
			Query dto.FindEntityElementsQuery
		}
		// FindRelationships holds details about calls to the FindRelationships method.
		FindRelationships []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Query is the query argument value.
			Query dto.FindRelationshipQuery
		}
		// GenerateBranchAndVersionID holds details about calls to the GenerateBranchAndVersionID method.
		GenerateBranchAndVersionID []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Tenant_id is the tenant_id argument value.
			Tenant_id string
			// Context_id is the context_id argument value.
			Context_id string
			// User_id is the user_id argument value.
			User_id string
			// Tx is the tx argument value.
			Tx *sql.Tx
		}
		// NavigateDataModel holds details about calls to the NavigateDataModel method.
		NavigateDataModel []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// TenantID is the tenantID argument value.
			TenantID string
			// Query is the query argument value.
			Query dto.NavigateDataModelQuery
		}
	}
	lockCreateBranch               sync.RWMutex
	lockCreateEntities             sync.RWMutex
	lockCreateEntityElements       sync.RWMutex
	lockCreateRelationships        sync.RWMutex
	lockCreateVersion              sync.RWMutex
	lockDeleteDataModel            sync.RWMutex
	lockFindEntities               sync.RWMutex
	lockFindEntityElements         sync.RWMutex
	lockFindRelationships          sync.RWMutex
	lockGenerateBranchAndVersionID sync.RWMutex
	lockNavigateDataModel          sync.RWMutex
}

// CreateBranch calls CreateBranchFunc.
func (mock *RepoMock) CreateBranch(ctx context.Context, tx *sql.Tx, branch dto.Branch) (string, error) {
	if mock.CreateBranchFunc == nil {
		panic("RepoMock.CreateBranchFunc: method is nil but Repo.CreateBranch was just called")
	}
	callInfo := struct {
		Ctx    context.Context
		Tx     *sql.Tx
		Branch dto.Branch
	}{
		Ctx:    ctx,
		Tx:     tx,
		Branch: branch,
	}
	mock.lockCreateBranch.Lock()
	mock.calls.CreateBranch = append(mock.calls.CreateBranch, callInfo)
	mock.lockCreateBranch.Unlock()
	return mock.CreateBranchFunc(ctx, tx, branch)
}

// CreateBranchCalls gets all the calls that were made to CreateBranch.
// Check the length with:
//
//	len(mockedRepo.CreateBranchCalls())
func (mock *RepoMock) CreateBranchCalls() []struct {
	Ctx    context.Context
	Tx     *sql.Tx
	Branch dto.Branch
} {
	var calls []struct {
		Ctx    context.Context
		Tx     *sql.Tx
		Branch dto.Branch
	}
	mock.lockCreateBranch.RLock()
	calls = mock.calls.CreateBranch
	mock.lockCreateBranch.RUnlock()
	return calls
}

// CreateEntities calls CreateEntitiesFunc.
func (mock *RepoMock) CreateEntities(ctx context.Context, tx *sql.Tx, entities dto.Entities) error {
	if mock.CreateEntitiesFunc == nil {
		panic("RepoMock.CreateEntitiesFunc: method is nil but Repo.CreateEntities was just called")
	}
	callInfo := struct {
		Ctx      context.Context
		Tx       *sql.Tx
		Entities dto.Entities
	}{
		Ctx:      ctx,
		Tx:       tx,
		Entities: entities,
	}
	mock.lockCreateEntities.Lock()
	mock.calls.CreateEntities = append(mock.calls.CreateEntities, callInfo)
	mock.lockCreateEntities.Unlock()
	return mock.CreateEntitiesFunc(ctx, tx, entities)
}

// CreateEntitiesCalls gets all the calls that were made to CreateEntities.
// Check the length with:
//
//	len(mockedRepo.CreateEntitiesCalls())
func (mock *RepoMock) CreateEntitiesCalls() []struct {
	Ctx      context.Context
	Tx       *sql.Tx
	Entities dto.Entities
} {
	var calls []struct {
		Ctx      context.Context
		Tx       *sql.Tx
		Entities dto.Entities
	}
	mock.lockCreateEntities.RLock()
	calls = mock.calls.CreateEntities
	mock.lockCreateEntities.RUnlock()
	return calls
}

// CreateEntityElements calls CreateEntityElementsFunc.
func (mock *RepoMock) CreateEntityElements(ctx context.Context, tx *sql.Tx, parentID string, entityElements dto.EntityElements) error {
	if mock.CreateEntityElementsFunc == nil {
		panic("RepoMock.CreateEntityElementsFunc: method is nil but Repo.CreateEntityElements was just called")
	}
	callInfo := struct {
		Ctx            context.Context
		Tx             *sql.Tx
		ParentID       string
		EntityElements dto.EntityElements
	}{
		Ctx:            ctx,
		Tx:             tx,
		ParentID:       parentID,
		EntityElements: entityElements,
	}
	mock.lockCreateEntityElements.Lock()
	mock.calls.CreateEntityElements = append(mock.calls.CreateEntityElements, callInfo)
	mock.lockCreateEntityElements.Unlock()
	return mock.CreateEntityElementsFunc(ctx, tx, parentID, entityElements)
}

// CreateEntityElementsCalls gets all the calls that were made to CreateEntityElements.
// Check the length with:
//
//	len(mockedRepo.CreateEntityElementsCalls())
func (mock *RepoMock) CreateEntityElementsCalls() []struct {
	Ctx            context.Context
	Tx             *sql.Tx
	ParentID       string
	EntityElements dto.EntityElements
} {
	var calls []struct {
		Ctx            context.Context
		Tx             *sql.Tx
		ParentID       string
		EntityElements dto.EntityElements
	}
	mock.lockCreateEntityElements.RLock()
	calls = mock.calls.CreateEntityElements
	mock.lockCreateEntityElements.RUnlock()
	return calls
}

// CreateRelationships calls CreateRelationshipsFunc.
func (mock *RepoMock) CreateRelationships(ctx context.Context, tx *sql.Tx, relationships dto.Relationships) error {
	if mock.CreateRelationshipsFunc == nil {
		panic("RepoMock.CreateRelationshipsFunc: method is nil but Repo.CreateRelationships was just called")
	}
	callInfo := struct {
		Ctx           context.Context
		Tx            *sql.Tx
		Relationships dto.Relationships
	}{
		Ctx:           ctx,
		Tx:            tx,
		Relationships: relationships,
	}
	mock.lockCreateRelationships.Lock()
	mock.calls.CreateRelationships = append(mock.calls.CreateRelationships, callInfo)
	mock.lockCreateRelationships.Unlock()
	return mock.CreateRelationshipsFunc(ctx, tx, relationships)
}

// CreateRelationshipsCalls gets all the calls that were made to CreateRelationships.
// Check the length with:
//
//	len(mockedRepo.CreateRelationshipsCalls())
func (mock *RepoMock) CreateRelationshipsCalls() []struct {
	Ctx           context.Context
	Tx            *sql.Tx
	Relationships dto.Relationships
} {
	var calls []struct {
		Ctx           context.Context
		Tx            *sql.Tx
		Relationships dto.Relationships
	}
	mock.lockCreateRelationships.RLock()
	calls = mock.calls.CreateRelationships
	mock.lockCreateRelationships.RUnlock()
	return calls
}

// CreateVersion calls CreateVersionFunc.
func (mock *RepoMock) CreateVersion(ctx context.Context, tx *sql.Tx, version dto.Version) (string, error) {
	if mock.CreateVersionFunc == nil {
		panic("RepoMock.CreateVersionFunc: method is nil but Repo.CreateVersion was just called")
	}
	callInfo := struct {
		Ctx     context.Context
		Tx      *sql.Tx
		Version dto.Version
	}{
		Ctx:     ctx,
		Tx:      tx,
		Version: version,
	}
	mock.lockCreateVersion.Lock()
	mock.calls.CreateVersion = append(mock.calls.CreateVersion, callInfo)
	mock.lockCreateVersion.Unlock()
	return mock.CreateVersionFunc(ctx, tx, version)
}

// CreateVersionCalls gets all the calls that were made to CreateVersion.
// Check the length with:
//
//	len(mockedRepo.CreateVersionCalls())
func (mock *RepoMock) CreateVersionCalls() []struct {
	Ctx     context.Context
	Tx      *sql.Tx
	Version dto.Version
} {
	var calls []struct {
		Ctx     context.Context
		Tx      *sql.Tx
		Version dto.Version
	}
	mock.lockCreateVersion.RLock()
	calls = mock.calls.CreateVersion
	mock.lockCreateVersion.RUnlock()
	return calls
}

// DeleteDataModel calls DeleteDataModelFunc.
func (mock *RepoMock) DeleteDataModel(ctx context.Context, tenantID string, contextID string, tx *sql.Tx) (*models.DeleteDataModelResponse, error) {
	if mock.DeleteDataModelFunc == nil {
		panic("RepoMock.DeleteDataModelFunc: method is nil but Repo.DeleteDataModel was just called")
	}
	callInfo := struct {
		Ctx       context.Context
		TenantID  string
		ContextID string
		Tx        *sql.Tx
	}{
		Ctx:       ctx,
		TenantID:  tenantID,
		ContextID: contextID,
		Tx:        tx,
	}
	mock.lockDeleteDataModel.Lock()
	mock.calls.DeleteDataModel = append(mock.calls.DeleteDataModel, callInfo)
	mock.lockDeleteDataModel.Unlock()
	return mock.DeleteDataModelFunc(ctx, tenantID, contextID, tx)
}

// DeleteDataModelCalls gets all the calls that were made to DeleteDataModel.
// Check the length with:
//
//	len(mockedRepo.DeleteDataModelCalls())
func (mock *RepoMock) DeleteDataModelCalls() []struct {
	Ctx       context.Context
	TenantID  string
	ContextID string
	Tx        *sql.Tx
} {
	var calls []struct {
		Ctx       context.Context
		TenantID  string
		ContextID string
		Tx        *sql.Tx
	}
	mock.lockDeleteDataModel.RLock()
	calls = mock.calls.DeleteDataModel
	mock.lockDeleteDataModel.RUnlock()
	return calls
}

// FindEntities calls FindEntitiesFunc.
func (mock *RepoMock) FindEntities(ctx context.Context, query dto.FindEntitiesQuery) (dto.Entities, error) {
	if mock.FindEntitiesFunc == nil {
		panic("RepoMock.FindEntitiesFunc: method is nil but Repo.FindEntities was just called")
	}
	callInfo := struct {
		Ctx   context.Context
		Query dto.FindEntitiesQuery
	}{
		Ctx:   ctx,
		Query: query,
	}
	mock.lockFindEntities.Lock()
	mock.calls.FindEntities = append(mock.calls.FindEntities, callInfo)
	mock.lockFindEntities.Unlock()
	return mock.FindEntitiesFunc(ctx, query)
}

// FindEntitiesCalls gets all the calls that were made to FindEntities.
// Check the length with:
//
//	len(mockedRepo.FindEntitiesCalls())
func (mock *RepoMock) FindEntitiesCalls() []struct {
	Ctx   context.Context
	Query dto.FindEntitiesQuery
} {
	var calls []struct {
		Ctx   context.Context
		Query dto.FindEntitiesQuery
	}
	mock.lockFindEntities.RLock()
	calls = mock.calls.FindEntities
	mock.lockFindEntities.RUnlock()
	return calls
}

// FindEntityElements calls FindEntityElementsFunc.
func (mock *RepoMock) FindEntityElements(ctx context.Context, query dto.FindEntityElementsQuery) (dto.EntityElements, error) {
	if mock.FindEntityElementsFunc == nil {
		panic("RepoMock.FindEntityElementsFunc: method is nil but Repo.FindEntityElements was just called")
	}
	callInfo := struct {
		Ctx   context.Context
		Query dto.FindEntityElementsQuery
	}{
		Ctx:   ctx,
		Query: query,
	}
	mock.lockFindEntityElements.Lock()
	mock.calls.FindEntityElements = append(mock.calls.FindEntityElements, callInfo)
	mock.lockFindEntityElements.Unlock()
	return mock.FindEntityElementsFunc(ctx, query)
}

// FindEntityElementsCalls gets all the calls that were made to FindEntityElements.
// Check the length with:
//
//	len(mockedRepo.FindEntityElementsCalls())
func (mock *RepoMock) FindEntityElementsCalls() []struct {
	Ctx   context.Context
	Query dto.FindEntityElementsQuery
} {
	var calls []struct {
		Ctx   context.Context
		Query dto.FindEntityElementsQuery
	}
	mock.lockFindEntityElements.RLock()
	calls = mock.calls.FindEntityElements
	mock.lockFindEntityElements.RUnlock()
	return calls
}

// FindRelationships calls FindRelationshipsFunc.
func (mock *RepoMock) FindRelationships(ctx context.Context, query dto.FindRelationshipQuery) (dto.Relationships, error) {
	if mock.FindRelationshipsFunc == nil {
		panic("RepoMock.FindRelationshipsFunc: method is nil but Repo.FindRelationships was just called")
	}
	callInfo := struct {
		Ctx   context.Context
		Query dto.FindRelationshipQuery
	}{
		Ctx:   ctx,
		Query: query,
	}
	mock.lockFindRelationships.Lock()
	mock.calls.FindRelationships = append(mock.calls.FindRelationships, callInfo)
	mock.lockFindRelationships.Unlock()
	return mock.FindRelationshipsFunc(ctx, query)
}

// FindRelationshipsCalls gets all the calls that were made to FindRelationships.
// Check the length with:
//
//	len(mockedRepo.FindRelationshipsCalls())
func (mock *RepoMock) FindRelationshipsCalls() []struct {
	Ctx   context.Context
	Query dto.FindRelationshipQuery
} {
	var calls []struct {
		Ctx   context.Context
		Query dto.FindRelationshipQuery
	}
	mock.lockFindRelationships.RLock()
	calls = mock.calls.FindRelationships
	mock.lockFindRelationships.RUnlock()
	return calls
}

// GenerateBranchAndVersionID calls GenerateBranchAndVersionIDFunc.
func (mock *RepoMock) GenerateBranchAndVersionID(ctx context.Context, tenant_id string, context_id string, user_id string, tx *sql.Tx) (string, string) {
	if mock.GenerateBranchAndVersionIDFunc == nil {
		panic("RepoMock.GenerateBranchAndVersionIDFunc: method is nil but Repo.GenerateBranchAndVersionID was just called")
	}
	callInfo := struct {
		Ctx        context.Context
		Tenant_id  string
		Context_id string
		User_id    string
		Tx         *sql.Tx
	}{
		Ctx:        ctx,
		Tenant_id:  tenant_id,
		Context_id: context_id,
		User_id:    user_id,
		Tx:         tx,
	}
	mock.lockGenerateBranchAndVersionID.Lock()
	mock.calls.GenerateBranchAndVersionID = append(mock.calls.GenerateBranchAndVersionID, callInfo)
	mock.lockGenerateBranchAndVersionID.Unlock()
	return mock.GenerateBranchAndVersionIDFunc(ctx, tenant_id, context_id, user_id, tx)
}

// GenerateBranchAndVersionIDCalls gets all the calls that were made to GenerateBranchAndVersionID.
// Check the length with:
//
//	len(mockedRepo.GenerateBranchAndVersionIDCalls())
func (mock *RepoMock) GenerateBranchAndVersionIDCalls() []struct {
	Ctx        context.Context
	Tenant_id  string
	Context_id string
	User_id    string
	Tx         *sql.Tx
} {
	var calls []struct {
		Ctx        context.Context
		Tenant_id  string
		Context_id string
		User_id    string
		Tx         *sql.Tx
	}
	mock.lockGenerateBranchAndVersionID.RLock()
	calls = mock.calls.GenerateBranchAndVersionID
	mock.lockGenerateBranchAndVersionID.RUnlock()
	return calls
}

// NavigateDataModel calls NavigateDataModelFunc.
func (mock *RepoMock) NavigateDataModel(ctx context.Context, tenantID string, query dto.NavigateDataModelQuery) (*dto.NavigateResult, error) {
	if mock.NavigateDataModelFunc == nil {
		panic("RepoMock.NavigateDataModelFunc: method is nil but Repo.NavigateDataModel was just called")
	}
	callInfo := struct {
		Ctx      context.Context
		TenantID string
		Query    dto.NavigateDataModelQuery
	}{
		Ctx:      ctx,
		TenantID: tenantID,
		Query:    query,
	}
	mock.lockNavigateDataModel.Lock()
	mock.calls.NavigateDataModel = append(mock.calls.NavigateDataModel, callInfo)
	mock.lockNavigateDataModel.Unlock()
	return mock.NavigateDataModelFunc(ctx, tenantID, query)
}

// NavigateDataModelCalls gets all the calls that were made to NavigateDataModel.
// Check the length with:
//
//	len(mockedRepo.NavigateDataModelCalls())
func (mock *RepoMock) NavigateDataModelCalls() []struct {
	Ctx      context.Context
	TenantID string
	Query    dto.NavigateDataModelQuery
} {
	var calls []struct {
		Ctx      context.Context
		TenantID string
		Query    dto.NavigateDataModelQuery
	}
	mock.lockNavigateDataModel.RLock()
	calls = mock.calls.NavigateDataModel
	mock.lockNavigateDataModel.RUnlock()
	return calls
}
