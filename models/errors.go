package models

// ErrorDetail represents individual error information
type ErrorDetail struct {
	Field   string `json:"field,omitempty"`
	Message string `json:"message"`
}

// CatalogError represents the standardized API error response
type CatalogError struct {
	TraceID string        `json:"trace_id"`
	Type    string        `json:"type"`
	Status  int           `json:"status,omitempty"`
	Title   string        `json:"title"`
	Detail  string        `json:"detail,omitempty"`
	Message string        `json:"message,omitempty"`
	Errors  []ErrorDetail `json:"errors,omitempty"`
}

// Error types constants
/*TODO might have to delete the full path and just keep the last part*/
const (
	ErrorTypeValidation             = "https://catalog.syniti.com/errors/validation-error"
	ErrorTypeUnsupportedMetaSchema  = "https://catalog.syniti.com/errors/unsupported-meta-schema-version"
	ErrorTypeInvalidVersion         = "https://catalog.syniti.com/errors/invalid-version"
	ErrorTypeIncompleteDatasetQuery = "https://catalog.syniti.com/errors/incomplete-dataset-query"
	ErrorTypeConflict               = "https://catalog.syniti.com/errors/conflict"
	ErrorTypeNotFound               = "https://catalog.syniti.com/errors/not-found"
	ErrorTypeUnauthorized           = "https://catalog.syniti.com/errors/unauthorized"
	ErrorTypeForbidden              = "https://catalog.syniti.com/errors/forbidden"
	ErrorTypeInternalServer         = "https://catalog.syniti.com/errors/internal-server-error"
)

// Error titles constants
const (
	ErrorTitleValidation             = "Validation Error"
	ErrorTitleUnsupportedMetaSchema  = "Unsupported Meta Schema Version"
	ErrorTitleInvalidVersion         = "Invalid Version"
	ErrorTitleIncompleteDatasetQuery = "Incomplete Dataset Query"
	ErrorTitleConflict               = "Conflict"
	ErrorTitleNotFound               = "Not Found"
	ErrorTitleUnauthorized           = "Unauthorized"
	ErrorTitleForbidden              = "Forbidden"
	ErrorTitleInternalServer         = "Internal Server Error"
)
